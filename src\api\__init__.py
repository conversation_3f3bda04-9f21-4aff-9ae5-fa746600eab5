"""
API Configuration and Documentation Setup

This module configures Flask-RESTX for API documentation and Swagger UI.
"""

from flask_restx import Api
from flask import Blueprint

# Create API blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Configure Flask-RESTX API with Swagger documentation
api = Api(
    api_bp,
    version='1.0',
    title='TuneScout API',
    description='A music discovery and search API powered by Discogs',
    doc='/docs/',  # Swagger UI will be available at /api/docs/
    contact='TuneScout Team',
    contact_email='<EMAIL>',
    license='MIT',
    license_url='https://opensource.org/licenses/MIT',
    authorizations={
        'apikey': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-API-KEY'
        },
        'oauth2': {
            'type': 'oauth2',
            'flow': 'authorizationCode',
            'authorizationUrl': 'https://www.discogs.com/oauth/authorize',
            'tokenUrl': 'https://api.discogs.com/oauth/access_token',
            'scopes': {
                'read': 'Read access to user data',
                'write': 'Write access to user data'
            }
        }
    },
    security='apikey'
)

# Define common API models for documentation
from flask_restx import fields

# Error response model
error_model = api.model('Error', {
    'error': fields.String(required=True, description='Error message'),
    'code': fields.Integer(description='Error code'),
    'details': fields.Raw(description='Additional error details')
})

# Health check response model
health_model = api.model('Health', {
    'status': fields.String(required=True, description='Service status', example='healthy'),
    'app': fields.String(description='Application name', example='TuneScout'),
    'version': fields.String(description='Application version', example='1.0.0'),
    'timestamp': fields.DateTime(description='Response timestamp')
})

# Search result model
search_result_model = api.model('SearchResult', {
    'id': fields.Integer(required=True, description='Discogs ID'),
    'title': fields.String(required=True, description='Title of the item'),
    'type': fields.String(required=True, description='Type of item (release, artist, label)', 
                          enum=['release', 'artist', 'label']),
    'year': fields.Integer(description='Release year'),
    'genre': fields.List(fields.String, description='List of genres'),
    'style': fields.List(fields.String, description='List of styles'),
    'thumb': fields.String(description='Thumbnail image URL'),
    'cover_image': fields.String(description='Cover image URL'),
    'resource_url': fields.String(description='Discogs API resource URL'),
    'uri': fields.String(description='Discogs URI'),
    'tracking_id': fields.String(description='Tracking ID for analytics')
})

# Search response model
search_response_model = api.model('SearchResponse', {
    'results': fields.List(fields.Nested(search_result_model), description='Search results'),
    'pagination': fields.Raw(description='Pagination information'),
    'total': fields.Integer(description='Total number of results'),
    'page': fields.Integer(description='Current page number'),
    'per_page': fields.Integer(description='Results per page')
})

# User model
user_model = api.model('User', {
    'id': fields.Integer(required=True, description='User ID'),
    'username': fields.String(required=True, description='Username'),
    'email': fields.String(required=True, description='Email address'),
    'created_at': fields.DateTime(description='Account creation date'),
    'updated_at': fields.DateTime(description='Last update date')
})

# Analytics tracking models
track_view_model = api.model('TrackView', {
    'tracking_id': fields.String(required=True, description='Tracking ID'),
    'result_id': fields.String(required=True, description='Result ID'),
    'result_type': fields.String(required=True, description='Result type')
})

track_click_model = api.model('TrackClick', {
    'tracking_id': fields.String(required=True, description='Tracking ID'),
    'result_id': fields.String(required=True, description='Result ID'),
    'result_type': fields.String(required=True, description='Result type'),
    'action': fields.String(description='Action performed', default='details')
})

# Filters response model
filters_model = api.model('Filters', {
    'genres': fields.List(fields.String, description='Available genres'),
    'years': fields.List(fields.Integer, description='Available years'),
    'types': fields.List(fields.String, description='Available search types')
})
