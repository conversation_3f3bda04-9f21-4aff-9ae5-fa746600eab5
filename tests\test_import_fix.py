#!/usr/bin/env python3
"""Test that the import fixes work"""
import os
import sys
import unittest

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestImportFix(unittest.TestCase):
    """Test that imports work without initialization issues"""

    def setUp(self):
        """Set up test environment"""
        os.environ['TESTING'] = '1'

    def tearDown(self):
        """Clean up test environment"""
        if 'TESTING' in os.environ:
            del os.environ['TESTING']

    def test_main_app_import(self):
        """Test that main app can be imported"""
        try:
            from src.main import app
            self.assertIsNotNone(app)
            print("✅ Main app imports successfully")
        except Exception as e:
            self.fail(f"Failed to import main app: {e}")
    
    def test_api_imports(self):
        """Test that API components can be imported"""
        try:
            from src.api import api_bp, api
            from src.api.search_api import search_ns
            from src.api.health_api import health_ns
            from src.api.user_api import user_ns
            
            self.assertIsNotNone(api_bp)
            self.assertIsNotNone(api)
            self.assertIsNotNone(search_ns)
            self.assertIsNotNone(health_ns)
            self.assertIsNotNone(user_ns)
            print("✅ API components import successfully")
        except Exception as e:
            self.fail(f"Failed to import API components: {e}")
    
    def test_services_lazy_loading(self):
        """Test that services can be lazy loaded"""
        try:
            from src.api.search_api import get_services
            discogs, analytics = get_services()
            
            self.assertIsNotNone(discogs)
            self.assertIsNotNone(analytics)
            print("✅ Services lazy loading works")
        except Exception as e:
            self.fail(f"Failed to lazy load services: {e}")
    
    def test_flask_app_creation(self):
        """Test that Flask app can be created and configured"""
        try:
            from src.main import app
            
            # Test that app is configured
            self.assertIsNotNone(app.config.get('SQLALCHEMY_DATABASE_URI'))
            
            # Test that blueprints are registered
            blueprint_names = [bp.name for bp in app.blueprints.values()]
            expected_blueprints = ['user', 'health', 'search', 'api']
            
            for bp_name in expected_blueprints:
                self.assertIn(bp_name, blueprint_names, f"Blueprint '{bp_name}' not registered")
            
            print("✅ Flask app creation and configuration works")
        except Exception as e:
            self.fail(f"Failed to create/configure Flask app: {e}")

if __name__ == "__main__":
    print("Testing import fixes...")
    print("=" * 50)
    unittest.main(verbosity=2)
