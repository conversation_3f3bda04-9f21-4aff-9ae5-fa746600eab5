#!/usr/bin/env python3
"""
Test runner for TuneScout application
"""
import os
import sys
import subprocess
import argparse

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def run_unit_tests():
    """Run unit tests"""
    print("Running unit tests...")
    print("=" * 50)
    
    try:
        # Run existing test files
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/test_services.py', 
            'tests/test_end_to_end.py',
            '-v'
        ], cwd=os.path.join(os.path.dirname(__file__), '..'))
        
        return result.returncode == 0
    except FileNotFoundError:
        print("pytest not found, running with unittest...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'unittest', 
                'tests.test_services',
                'tests.test_end_to_end',
                '-v'
            ], cwd=os.path.join(os.path.dirname(__file__), '..'))
            
            return result.returncode == 0
        except Exception as e:
            print(f"Failed to run unit tests: {e}")
            return False

def run_startup_tests():
    """Run application startup tests"""
    print("\nRunning startup tests...")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 'tests/test_app_startup.py'
        ], cwd=os.path.join(os.path.dirname(__file__), '..'))
        
        return result.returncode == 0
    except Exception as e:
        print(f"Failed to run startup tests: {e}")
        return False

def run_integration_tests():
    """Run integration tests (requires running application)"""
    print("\nRunning integration tests...")
    print("=" * 50)
    print("Note: These tests require the application to be running!")
    print("Start the app first with: python -m src.main")
    print()
    
    input("Press Enter when the application is running, or Ctrl+C to skip...")
    
    try:
        # Run Swagger tests
        result1 = subprocess.run([
            sys.executable, 'tests/test_swagger.py'
        ], cwd=os.path.join(os.path.dirname(__file__), '..'))
        
        # Run chat interface tests
        result2 = subprocess.run([
            sys.executable, 'tests/test_chat_interface.py'
        ], cwd=os.path.join(os.path.dirname(__file__), '..'))
        
        return result1.returncode == 0 and result2.returncode == 0
    except KeyboardInterrupt:
        print("Integration tests skipped.")
        return True
    except Exception as e:
        print(f"Failed to run integration tests: {e}")
        return False

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='Run TuneScout tests')
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--startup', action='store_true', help='Run startup tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    
    args = parser.parse_args()
    
    if not any([args.unit, args.startup, args.integration, args.all]):
        args.all = True  # Default to all tests
    
    print("TuneScout Test Runner")
    print("=" * 60)
    
    success = True
    
    if args.all or args.unit:
        success &= run_unit_tests()
    
    if args.all or args.startup:
        success &= run_startup_tests()
    
    if args.all or args.integration:
        success &= run_integration_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
    else:
        print("❌ Some tests failed. Check the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
