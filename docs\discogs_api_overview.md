# Discogs API Overview

## General Information

The Discogs API v2.0 is a RESTful interface that provides access to Discogs data in JSON format. It allows developers to access information about:
- Database objects (Artists, Releases, Labels)
- User Collections and Wantlists
- Marketplace Listings

## Authentication Requirements

- **User-Agent Header**: All applications must provide a User-Agent string that identifies the application
  - Example: `MyDiscogsClient/1.0 +http://mydiscogsclient.org`
  - Bad examples: generic browser user agents or vague identifiers

- **OAuth Authentication**: For accessing user-specific data and higher rate limits
  - Discogs Auth Flow
  - OAuth Flow with Request Token and Access Token URLs

## Rate Limiting

- **Authenticated Requests**: 60 requests per minute
- **Unauthenticated Requests**: 25 requests per minute
- Rate limiting uses a moving average over a 60-second window
- Headers provided:
  - `X-Discogs-Ratelimit`: Total requests allowed per minute
  - `X-Discogs-Ratelimit-Used`: Number of requests made in current window
  - `X-Discogs-Ratelimit-Remaining`: Remaining requests in current window

## Pagination

- Default: 50 items per page
- Can be customized up to 100 items per page using `page` and `per_page` parameters
- Example: `GET https://api.discogs.com/artists/1/releases?page=2&per_page=75`
- Responses include pagination information in both headers and response body

## Data Licensing

- Some data is available under CC0 No Rights Reserved license
- Some data is restricted as defined in API Terms of Use
- Monthly data dumps are available under CC0 No Rights Reserved license

## Available Client Libraries

| Language | Type | Maintainer | URL |
| --- | --- | --- | --- |
| Python | Client | joalla | https://github.com/joalla/discogs_client |
| Python | Example | jesseward | https://github.com/jesseward/discogs-oauth-example |
| Node.js | Client | bartve | https://github.com/bartve/disconnect |
| PHP | Client | ricbra | https://github.com/ricbra/php-discogs-api |
| Ruby | Client | buntine | https://github.com/buntine/discogs |

## API Versioning

- Current version: v2
- Accept header format: `application/vnd.discogs.v2.html+json`
- Text formatting options:
  - `application/vnd.discogs.v2.html+json`
  - `application/vnd.discogs.v2.plaintext+json`
  - `application/vnd.discogs.v2.discogs+json` (default)

## Basic Usage Example

```bash
curl https://api.discogs.com/releases/249504 --user-agent "FooBarApp/3.0"
```

## Next Steps for Implementation

1. Explore the Python client libraries in detail
2. Understand OAuth implementation for user authentication
3. Identify key endpoints needed for the application
4. Plan rate limiting strategy for the application
