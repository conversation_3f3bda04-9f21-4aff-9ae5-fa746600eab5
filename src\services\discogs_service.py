"""
Discogs API Service

This module provides functionality for interacting with the Discogs API,
including rate limiting and request throttling.
"""

import os
import time
import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import discogs_client
from discogs_client.exceptions import DiscogsAPIError

logger = logging.getLogger(__name__)

class RateLimiter:
    """Rate limiter for API requests."""
    
    def __init__(self, requests_per_minute: int = 60):
        """
        Initialize the rate limiter.
        
        Args:
            requests_per_minute: Maximum number of requests per minute
        """
        self.requests_per_minute = requests_per_minute
        self.request_timestamps = []
        self.min_interval = 60.0 / requests_per_minute  # Minimum interval between requests in seconds
    
    def wait_if_needed(self) -> float:
        """
        Wait if necessary to comply with rate limits.
        
        Returns:
            Time waited in seconds
        """
        now = time.time()
        
        # Remove timestamps older than 1 minute
        self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]
        
        # If we've made too many requests in the last minute, wait
        if len(self.request_timestamps) >= self.requests_per_minute:
            # Calculate how long to wait
            oldest_timestamp = min(self.request_timestamps)
            wait_time = 60 - (now - oldest_timestamp)
            
            if wait_time > 0:
                logger.info(f"Rate limit reached. Waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)
                now = time.time()  # Update current time after waiting
                return wait_time
        
        # If we've made a request very recently, ensure minimum interval
        if self.request_timestamps and now - self.request_timestamps[-1] < self.min_interval:
            wait_time = self.min_interval - (now - self.request_timestamps[-1])
            time.sleep(wait_time)
            now = time.time()  # Update current time after waiting
            return wait_time
        
        # Record this request
        self.request_timestamps.append(now)
        return 0.0
    
    def update_limits(self, headers: Dict[str, str]) -> None:
        """
        Update rate limits based on response headers.
        
        Args:
            headers: Response headers
        """
        # Extract rate limit information from headers
        remaining = headers.get('X-Discogs-Ratelimit-Remaining')
        total = headers.get('X-Discogs-Ratelimit')
        
        if remaining and total:
            try:
                remaining = int(remaining)
                total = int(total)
                
                # If we're close to the limit, adjust our rate
                if remaining < total * 0.2:  # Less than 20% remaining
                    self.requests_per_minute = max(1, self.requests_per_minute // 2)
                    self.min_interval = 60.0 / self.requests_per_minute
                    logger.warning(f"Rate limit running low ({remaining}/{total}). Reducing rate to {self.requests_per_minute} requests per minute")
                elif remaining > total * 0.8:  # More than 80% remaining
                    # Gradually increase back to normal if we've been throttled
                    if self.requests_per_minute < 60:
                        self.requests_per_minute = min(60, self.requests_per_minute * 2)
                        self.min_interval = 60.0 / self.requests_per_minute
                        logger.info(f"Rate limit healthy ({remaining}/{total}). Increasing rate to {self.requests_per_minute} requests per minute")
            except (ValueError, TypeError):
                pass

class DiscogsService:
    """Service for interacting with the Discogs API."""
    
    def __init__(self, token: Optional[str] = None, user_agent: Optional[str] = None):
        """
        Initialize the Discogs service.
        
        Args:
            token: Discogs Personal Access Token
            user_agent: User agent string
        """
        self.token = token or os.environ.get('DISCOGS_TOKEN', '')
        self.user_agent = user_agent or 'TuneScout/1.0'
        
        # Initialize rate limiters
        self.authenticated_limiter = RateLimiter(requests_per_minute=60)
        self.unauthenticated_limiter = RateLimiter(requests_per_minute=25)
        
        # Initialize client
        self._init_client()
        
        # Initialize session with retry logic
        self.session = self._create_session_with_retry()
        
        # Cache for search results
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.cache_max_size = 100
    
    def _init_client(self) -> None:
        """Initialize the Discogs client."""
        try:
            if self.token:
                self.client = discogs_client.Client(self.user_agent, user_token=self.token)
                logger.info("Discogs client initialized with user token")
            else:
                self.client = discogs_client.Client(self.user_agent)
                logger.info("Discogs client initialized without authentication")
        except Exception as e:
            logger.error(f"Error initializing Discogs client: {str(e)}")
            self.client = None
    
    def _create_session_with_retry(self) -> requests.Session:
        """Create a requests session with retry logic."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=5,  # Maximum number of retries
            backoff_factor=1,  # Exponential backoff
            status_forcelist=[429, 500, 502, 503, 504],  # Status codes to retry on
            allowed_methods=["GET", "POST"],  # Methods to retry
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        
        return session
    
    def _get_rate_limiter(self) -> RateLimiter:
        """Get the appropriate rate limiter based on authentication status."""
        if self.token:
            return self.authenticated_limiter
        else:
            return self.unauthenticated_limiter
    
    def _get_cache_key(self, query: str, **kwargs) -> str:
        """Generate a cache key from query parameters."""
        key_parts = [query]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        return "|".join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get results from cache if available and not expired."""
        if cache_key in self.cache:
            timestamp, data = self.cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                logger.info(f"Cache hit for {cache_key}")
                return data
            else:
                # Remove expired entry
                del self.cache[cache_key]
        return None
    
    def _add_to_cache(self, cache_key: str, data: Dict[str, Any]) -> None:
        """Add results to cache."""
        # Limit cache size
        if len(self.cache) >= self.cache_max_size:
            # Remove oldest entry
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][0])
            del self.cache[oldest_key]
        
        self.cache[cache_key] = (time.time(), data)
    
    def search(self, query: str, search_type: Optional[str] = None, 
              artist: Optional[str] = None, title: Optional[str] = None,
              genre: Optional[str] = None, year: Optional[str] = None,
              page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """
        Search for releases, artists, or labels.
        
        Args:
            query: Search query
            search_type: Type of search ('artist', 'album', 'track', 'label')
            artist: Artist name
            title: Release title
            genre: Genre
            year: Year
            page: Page number
            per_page: Results per page
            
        Returns:
            Dictionary containing search results
        """
        try:
            # Generate cache key
            cache_key = self._get_cache_key(
                query,
                type=search_type,
                artist=artist,
                title=title,
                genre=genre,
                year=year,
                page=page,
                per_page=per_page
            )
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Map search_type to Discogs API type
            discogs_type = None
            if search_type:
                type_mapping = {
                    'artist': 'artist',
                    'album': 'release',
                    'track': 'release',
                    'label': 'label'
                }
                discogs_type = type_mapping.get(search_type.lower())
            
            # Perform search
            search_params = {'page': page, 'per_page': per_page}
            if discogs_type:
                search_params['type'] = discogs_type
            if artist:
                search_params['artist'] = artist
            if title:
                search_params['title'] = title
            if genre:
                search_params['genre'] = genre
            if year:
                search_params['year'] = year

            api_results = self.client.search(query, **search_params)
            
            results_list = []
            if hasattr(api_results, 'page'): # Check if it's a PaginatedList
                for item in api_results.page(page): # Iterate through the current page
                    item_data = item.data # discogs_client objects have a .data attribute which is a dict
                    if 'cover_image' in item_data and item_data['cover_image']:
                        item_data['image_url'] = item_data['cover_image']
                    elif 'thumb' in item_data and item_data['thumb']:
                        item_data['image_url'] = item_data['thumb']
                    else:
                        item_data['image_url'] = None # Or a placeholder URL
                    results_list.append(item_data)
            else: # Fallback for non-paginated or direct list results (if any)
                 for item in api_results: # Assuming api_results might be a simple list
                    item_data = item.data if hasattr(item, 'data') else item # Handle both object and dict items
                    if isinstance(item_data, dict):
                        if 'cover_image' in item_data and item_data['cover_image']:
                            item_data['image_url'] = item_data['cover_image']
                        elif 'thumb' in item_data and item_data['thumb']:
                            item_data['image_url'] = item_data['thumb']
                        else:
                            item_data['image_url'] = None
                        results_list.append(item_data)
                    else:
                        results_list.append(item) # If not a dict, append as is


            # Construct response
            response_data = {
                'results': results_list,
                'pagination': {
                    'page': page,
                    'pages': api_results.pages if hasattr(api_results, 'pages') else 1,
                    'per_page': per_page,
                    'items': api_results.count if hasattr(api_results, 'count') else len(results_list),
                    'urls': {} # Placeholder for next/prev URLs if needed
                }
            }

            # Add pagination URLs if available
            if hasattr(api_results, 'next_url') and api_results.next_url:
                response_data['pagination']['urls']['next'] = api_results.next_url
            if hasattr(api_results, 'prev_url') and api_results.prev_url:
                response_data['pagination']['urls']['prev'] = api_results.prev_url

            # Update rate limiter based on response headers
            # This part might need adjustment if self.client.search doesn't directly expose headers for search
            # For now, we'll assume headers are not directly available here post-search.
            # rate_limiter.update_limits(api_results.headers) 

            # Add to cache
            self._add_to_cache(cache_key, response_data)
            
            return response_data
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.search(query, search_type, artist, title, genre, year, page, per_page)
            
            return {'error': str(e), 'results': [], 'pagination': {'items': 0}}
        except Exception as e:
            logger.error(f"Error searching Discogs: {str(e)}")
            return {'error': str(e), 'results': [], 'pagination': {'items': 0}}
    
    def get_artist(self, artist_id: int) -> Dict[str, Any]:
        """
        Get artist details.
        
        Args:
            artist_id: Artist ID
            
        Returns:
            Dictionary containing artist details
        """
        try:
            # Generate cache key
            cache_key = f"artist_{artist_id}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get artist
            artist = self.client.artist(artist_id)
            
            # Format results
            result = {
                'id': artist.id,
                'name': artist.name,
                'profile': artist.profile,
                'urls': artist.urls,
                'images': [{'uri': img.uri, 'type': img.type} for img in artist.images],
                'releases': []
            }
            
            # Get releases
            releases_page = artist.releases.page(1)
            for release in releases_page:
                result['releases'].append({
                    'id': release.id,
                    'title': release.title,
                    'year': release.year if hasattr(release, 'year') else None,
                    'type': release.type,
                    'thumb': release.thumb if hasattr(release, 'thumb') else None
                })
            
            # Cache results
            self._add_to_cache(cache_key, result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_artist(artist_id)
            
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Error getting artist: {str(e)}")
            return {'error': str(e)}
    
    def get_release(self, release_id: int) -> Dict[str, Any]:
        """
        Get release details.
        
        Args:
            release_id: Release ID
            
        Returns:
            Dictionary containing release details
        """
        try:
            # Generate cache key
            cache_key = f"release_{release_id}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get release
            release = self.client.release(release_id)
            
            # Format results
            result = {
                'id': release.id,
                'title': release.title,
                'artists': [{'id': a.id, 'name': a.name} for a in release.artists],
                'year': release.year,
                'labels': [{'id': l.id, 'name': l.name} for l in release.labels],
                'formats': release.formats,
                'genres': release.genres,
                'styles': release.styles,
                'tracklist': [{'position': t.position, 'title': t.title, 'duration': t.duration} for t in release.tracklist],
                'images': [{'uri': img.uri, 'type': img.type} for img in release.images],
                'videos': [{'url': v.url, 'title': v.title} for v in release.videos] if hasattr(release, 'videos') else []
            }
            
            # Cache results
            self._add_to_cache(cache_key, result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_release(release_id)
            
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Error getting release: {str(e)}")
            return {'error': str(e)}
    
    def get_label(self, label_id: int) -> Dict[str, Any]:
        """
        Get label details.
        
        Args:
            label_id: Label ID
            
        Returns:
            Dictionary containing label details
        """
        try:
            # Generate cache key
            cache_key = f"label_{label_id}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get label
            label = self.client.label(label_id)
            
            # Format results
            result = {
                'id': label.id,
                'name': label.name,
                'profile': label.profile,
                'contact_info': label.contact_info,
                'urls': label.urls,
                'images': [{'uri': img.uri, 'type': img.type} for img in label.images],
                'releases': []
            }
            
            # Get releases
            releases_page = label.releases.page(1)
            for release in releases_page:
                result['releases'].append({
                    'id': release.id,
                    'title': release.title,
                    'artist': release.artist,
                    'year': release.year if hasattr(release, 'year') else None,
                    'thumb': release.thumb if hasattr(release, 'thumb') else None
                })
            
            # Cache results
            self._add_to_cache(cache_key, result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_label(label_id)
            
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Error getting label: {str(e)}")
            return {'error': str(e)}
    
    def get_user_collection(self, username: str, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        Get a user's collection.
        
        Args:
            username: Discogs username
            page: Page number
            per_page: Results per page
            
        Returns:
            Dictionary containing collection details
        """
        try:
            # Generate cache key
            cache_key = f"collection_{username}_{page}_{per_page}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get user
            user = self.client.user(username)
            
            # Get collection
            collection_page = user.collection_folders[0].releases.page(page, per_page=per_page)
            
            # Format results
            result = {
                'pagination': {
                    'page': collection_page.page,
                    'pages': collection_page.pages,
                    'per_page': collection_page.per_page,
                    'items': collection_page.count
                },
                'releases': []
            }
            
            for item in collection_page:
                release = item.release
                result['releases'].append({
                    'id': release.id,
                    'title': release.title,
                    'artist': release.artist,
                    'year': release.year if hasattr(release, 'year') else None,
                    'thumb': release.thumb if hasattr(release, 'thumb') else None,
                    'date_added': item.date_added
                })
            
            # Cache results
            self._add_to_cache(cache_key, result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_user_collection(username, page, per_page)
            
            return {'error': str(e), 'releases': [], 'pagination': {'items': 0}}
        except Exception as e:
            logger.error(f"Error getting user collection: {str(e)}")
            return {'error': str(e), 'releases': [], 'pagination': {'items': 0}}
    
    def get_user_wantlist(self, username: str, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        Get a user's wantlist.
        
        Args:
            username: Discogs username
            page: Page number
            per_page: Results per page
            
        Returns:
            Dictionary containing wantlist details
        """
        try:
            # Generate cache key
            cache_key = f"wantlist_{username}_{page}_{per_page}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get user
            user = self.client.user(username)
            
            # Get wantlist
            wantlist_page = user.wantlist.page(page, per_page=per_page)
            
            # Format results
            result = {
                'pagination': {
                    'page': wantlist_page.page,
                    'pages': wantlist_page.pages,
                    'per_page': wantlist_page.per_page,
                    'items': wantlist_page.count
                },
                'releases': []
            }
            
            for item in wantlist_page:
                release = item.release
                result['releases'].append({
                    'id': release.id,
                    'title': release.title,
                    'artist': release.artist,
                    'year': release.year if hasattr(release, 'year') else None,
                    'thumb': release.thumb if hasattr(release, 'thumb') else None,
                    'date_added': item.date_added,
                    'notes': item.notes
                })
            
            # Cache results
            self._add_to_cache(cache_key, result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_user_wantlist(username, page, per_page)
            
            return {'error': str(e), 'releases': [], 'pagination': {'items': 0}}
        except Exception as e:
            logger.error(f"Error getting user wantlist: {str(e)}")
            return {'error': str(e), 'releases': [], 'pagination': {'items': 0}}
    
    def get_marketplace_listings(self, release_id: int, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        Get marketplace listings for a release.
        
        Args:
            release_id: Release ID
            page: Page number
            per_page: Results per page
            
        Returns:
            Dictionary containing marketplace listings
        """
        try:
            # Generate cache key
            cache_key = f"marketplace_{release_id}_{page}_{per_page}"
            
            # Check cache
            cached_results = self._get_from_cache(cache_key)
            if cached_results:
                return cached_results
            
            # Apply rate limiting
            rate_limiter = self._get_rate_limiter()
            rate_limiter.wait_if_needed()
            
            # Get release
            release = self.client.release(release_id)
            
            # Get marketplace listings
            listings_page = release.marketplace.page(page, per_page=per_page)
            
            # Format results
            result = {
                'pagination': {
                    'page': listings_page.page,
                    'pages': listings_page.pages,
                    'per_page': listings_page.per_page,
                    'items': listings_page.count
                },
                'listings': []
            }
            
            for listing in listings_page:
                result['listings'].append({
                    'id': listing.id,
                    'price': listing.price,
                    'seller': listing.seller,
                    'condition': listing.condition,
                    'sleeve_condition': listing.sleeve_condition,
                    'comments': listing.comments,
                    'ships_from': listing.ships_from,
                    'url': listing.url
                })
            
            # Cache results with shorter TTL (prices change frequently)
            self.cache[cache_key] = (time.time(), result)
            
            return result
        except DiscogsAPIError as e:
            logger.error(f"Discogs API error: {str(e)}")
            
            # Handle rate limiting errors
            if hasattr(e, 'status_code') and e.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting before retry.")
                time.sleep(10)  # Wait 10 seconds before retry
                return self.get_marketplace_listings(release_id, page, per_page)
            
            return {'error': str(e), 'listings': [], 'pagination': {'items': 0}}
        except Exception as e:
            logger.error(f"Error getting marketplace listings: {str(e)}")
            return {'error': str(e), 'listings': [], 'pagination': {'items': 0}}
    
    def clear_cache(self) -> None:
        """Clear the cache."""
        self.cache = {}
        logger.info("Cache cleared")
