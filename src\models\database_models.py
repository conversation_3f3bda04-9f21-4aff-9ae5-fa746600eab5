"""
Database Models for User Profiles and History

This module defines the database models for user profiles and chat history
using SQLAlchemy ORM.
"""

import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class User(Base):
    """User model for storing user account information."""
    
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(100), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # OAuth tokens
    oauth_token = Column(String(255), nullable=True)
    oauth_token_secret = Column(String(255), nullable=True)
    
    # Relationships
    profile = relationship("UserProfile", uselist=False, back_populates="user", cascade="all, delete-orphan")
    chat_sessions = relationship("ChatSession", back_populates="user", cascade="all, delete-orphan")
    settings = relationship("UserSettings", uselist=False, back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"


class UserProfile(Base):
    """User profile model for storing additional user information."""
    
    __tablename__ = 'user_profiles'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    display_name = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    location = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    
    # Discogs-specific profile data
    discogs_id = Column(Integer, nullable=True)
    discogs_username = Column(String(100), nullable=True)
    discogs_url = Column(String(255), nullable=True)
    discogs_collection_count = Column(Integer, default=0)
    discogs_wantlist_count = Column(Integer, default=0)
    
    # Preferences and interests (stored as JSON)
    music_preferences = Column(JSON, nullable=True)
    favorite_genres = Column(JSON, nullable=True)
    favorite_artists = Column(JSON, nullable=True)
    
    # Relationship
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id})>"


class UserSettings(Base):
    """User settings model for storing application settings."""
    
    __tablename__ = 'user_settings'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # API settings
    discogs_api_key = Column(String(255), nullable=True)
    discogs_api_secret = Column(String(255), nullable=True)
    
    # UI preferences
    theme = Column(String(50), default='light')
    items_per_page = Column(Integer, default=10)
    
    # Notification settings
    email_notifications = Column(Boolean, default=False)
    
    # Privacy settings
    share_collection = Column(Boolean, default=False)
    share_wantlist = Column(Boolean, default=False)
    
    # Relationship
    user = relationship("User", back_populates="settings")
    
    def __repr__(self):
        return f"<UserSettings(id={self.id}, user_id={self.user_id})>"


class ChatSession(Base):
    """Chat session model for storing chat sessions."""
    
    __tablename__ = 'chat_sessions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ChatSession(id={self.id}, user_id={self.user_id})>"


class ChatMessage(Base):
    """Chat message model for storing individual chat messages."""
    
    __tablename__ = 'chat_messages'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey('chat_sessions.id'), nullable=False)
    role = Column(String(20), nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)
    
    # For tracking context and references
    context = Column(JSON, nullable=True)
    references = Column(JSON, nullable=True)
    
    # Relationship
    session = relationship("ChatSession", back_populates="messages")
    
    def __repr__(self):
        return f"<ChatMessage(id={self.id}, session_id={self.session_id}, role='{self.role}')>"


class MusicEntity(Base):
    """Base model for music entities (artists, albums, tracks, etc.)."""
    
    __tablename__ = 'music_entities'
    
    id = Column(Integer, primary_key=True)
    discogs_id = Column(Integer, nullable=False)
    type = Column(String(20), nullable=False)  # 'artist', 'release', 'master', 'label'
    name = Column(String(255), nullable=False)
    year = Column(Integer, nullable=True)
    data = Column(JSON, nullable=True)  # Additional entity data
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __repr__(self):
        return f"<MusicEntity(id={self.id}, type='{self.type}', name='{self.name}')>"


class UserInteraction(Base):
    """Model for tracking user interactions with music entities."""
    
    __tablename__ = 'user_interactions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    entity_id = Column(Integer, ForeignKey('music_entities.id'), nullable=False)
    interaction_type = Column(String(50), nullable=False)  # 'view', 'search', 'like', etc.
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)
    
    # Additional interaction data
    data = Column(JSON, nullable=True)
    
    def __repr__(self):
        return f"<UserInteraction(id={self.id}, user_id={self.user_id}, entity_id={self.entity_id})>"
