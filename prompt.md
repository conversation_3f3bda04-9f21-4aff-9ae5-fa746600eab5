use mcp tools whenever possible or needed per usecase to get things done without user (berry) interaction as much as possible. this projects root is D:\berry\Projects\TuneScout

we have docker desktop running you can access and use docker compose commands etc to verify logs, restart and build containers.

we also have the project in a venv locally, so when using make sure to have activated env so when you activate that and run inside there you can test stuff too.

GLOBAL BEHAVIOUR • Use MCP tools directly (`list_directory`, `read_file`, `edit_block`, `execute_command`, `mcp.browser.open`, `mcp.browser.screenshot`, `create_entities`, `update_entity`, `search_nodes`, …).
• Skip intermediary scripts unless the task is impossible without them; if scripting is required, explain why first.
• **No shortcuts**: never mask issues with fake data, stubs, or placeholder code; always apply genuine fixes that resolve the underlying problem.
• After **each** action: stream raw output (logs, diffstat, screenshot path) plus a one-liner summary.
• **CRITICAL SAFETY CHECKS:**

* Confirm before deleting entire directories or files
* Verify git branch/status after every git operation
* Confirm commits actually happened before proceeding
* Check file existence before assuming operations succeeded • Confirm only before **potentially destructive** changes (file deletes, DB resets, graph wipes, directory removals, git force operations).
  • Stay concise—focus on what just happened and what's next.
* Do not create extra and new md docs or readme files for too manyh things we have github for issues and code should be clear enough in most cases.
─────────────────────── CODE QUALITY ENFORCEMENT **Dev-Tools Container (separate from production):**

* Dedicated `dev_tools` service with quality tools
* Mounts all code directories read-only
* Zero production impact, easy cleanup
* Test file should go into the test folder!
**Pre-action Quality Checks (always run before any code changes):**

0. **Conflict Detection**: `find /workspace -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.json" -o -name "*.yml" -o -name "*.md" | xargs grep -l "<<<<<<< HEAD\|=======\|>>>>>>>" || echo "No conflicts"` **If conflicts detected**: STOP and resolve before continuing

**Container Health Validation:** Before any service interaction, verify:

```bash
docker-compose -f D:\berry\Projects\TuneScout\docker-compose.yml ps --format json | jq -r '.[] | select(.Health != "healthy" and .Health != "") | .Name + ": " + .Health'
```

─────────────────────── PERSISTENT PROJECT MEMORY  (MCP server: `knowledgeGraph`) Key : absolute project root (`WORKDIR`)

On every new chat

1. `search_nodes({ filter: { projectPath: WORKDIR } })`
2. If none → `create_entities([{ name: WORKDIR, projectPath: WORKDIR }])`
3. Summarise `lastSnapshot` in ≤ 3 lines so the user knows where we left off.

After each meaningful iteration

1. Build  `snapshot = { iteration, buildStatus, tests, runtimeStatus,                     uiVerdict, codeQuality, performance, todo /* ≤ 5 bullets */ }`
2. `update_entity({ name: WORKDIR,                  set: { lastSnapshot: snapshot,                         updated: <ISO-timestamp> } })`
   • Keep only one `lastSnapshot` per project (no history flood).
   • Show the snapshot only when the user asks.

─────────────────────── PR REVIEW WORKFLOW Command: `PR_REVIEW <pr_number>` or `PR_REVIEW <pr_url>` Repository: [https://github.com/BerryKuipers/TuneScout](https://github.com/BerryKuipers/TuneScout)

**MCP TOOL USAGE:**

* **Local Git Operations**: Use git MCP tools (`git_status`, `git_commit`, `git_checkout`, etc.)
* **Remote GitHub Operations**: Use GitHub MCP tools (PR details, comments, merge, etc.)

gradio (older) frontend on [http://127.0.0.1:761/](http://127.0.0.1:761/) nextjs frontend on [http://127.0.0.1:3000/](http://127.0.0.1:3000/) (if not check .env or dockerfile)

FLOW:

1. **Fetch PR** Use **GitHub MCP** to get PR details, comments, suggestions from BerryKuipers/TuneScout

   * **Extract source branch name** from PR details (e.g., "feature/new-ui", "bugfix/auth-issue")
   * **Do NOT use generic names** like "pr-3" - use the actual branch name
2. **Critical Analysis** Review all feedback with critical mind - assess validity, implementation approach, potential side effects

   * **Evaluate each suggestion**: Does it actually improve the code?
   * **Consider context**: Does the suggestion understand the full system?
   * **Check for conflicts**: Does it break existing patterns/architecture?
   * **Assess performance**: Will this suggestion cause performance issues?
   * **Security implications**: Does the change introduce vulnerabilities?
   * **DRY/SOLID compliance**: Does it follow project coding principles?
   * **Reject inappropriate suggestions**: Don't implement if it worsens code quality
   * **Document reasoning**: Explain why suggestions were accepted/rejected
3. **Checkout Branch** Use **git MCP tools** for all local operations:

   * **Get branch name**: Use GitHub MCP to fetch PR details and extract source branch name
   * `git fetch origin <actual-branch-name>` (fetch the real feature branch)
   * `git checkout <actual-branch-name>` (checkout existing remote branch)
   * `git status` (verify branch switch - MUST show actual branch name)
   * `git log --oneline -3` (verify PR commits are present)
   * `git diff development` (verify changes present)
   * **CRITICAL**: Never create new local branches - always checkout existing remote branches
   * **VERIFY**: If branch switch failed, STOP and report error
4. **Quality Gate** Run full dev-tools container quality checks before any changes
5. **Implement Changes** Apply suggestions via `edit_block`, following DRY/SOLID principles
6. **Quality Validation** Re-run all quality checks, fix any violations via dev-tools container
7. **MANDATORY DOCKER BUILD & DEPLOY PROTOCOL**

   * After **any** file edit: `git diff --name-only` to detect changes
   * If changes detected: \*\*ALWAYS use \*\*\`\` for affected services
   * **NEVER use simple restart** - always rebuild and redeploy
   * **Example**: `docker-compose -f D:\berry\Projects\TuneScout\docker-compose.yml up -d --build`
   * Run quality & test gates on rebuilt services
8. **Log Verification Protocol**

   * **MANDATORY**: After each build/deploy, check logs for at least 30 seconds
   * `docker-compose -f D:\berry\Projects\TuneScout\docker-compose.yml logs -f --tail=50 <service_name>`
   * **Look for**: startup errors, connection issues, missing dependencies, crashes
   * **STOP and fix** if any errors detected in logs
   * **Report to user** any warnings or issues found
9. **Commit Quality Fixes**

   * `git add -A` (stage all changes)
   * `git status` (verify files staged)
   * `git commit -m "Apply quality fixes and PR suggestions"`
   * `git log --oneline -1` (VERIFY commit actually happened)
   * **CRITICAL**: If commit failed, STOP - do not proceed to merge
10. **Resolve Comments** Mark each addressed comment as resolved via GitHub MCP

    * **For implemented suggestions**: Explain what was changed and why
    * **For rejected suggestions**: Provide clear reasoning why suggestion wasn't appropriate
    * **Alternative solutions**: If suggestion had merit but implementation differed, explain approach taken
    * **No blind acceptance**: Every suggestion must pass critical evaluation first
11. **Build & Test** Full build → test → container logs verification → performance check
12. **Runtime Check** Deploy locally, screenshot UI, verify no exceptions
13. **Security Check** Run security scan via dev-tools container, verify no new vulnerabilities
14. **MANDATORY USER VERIFICATION**

    * **NEVER finalize without user confirmation**
    * Show final logs, test results, and screenshot
    * Wait for user approval before proceeding with merge
    * Present summary: "Ready to merge? Logs clean, tests pass, UI functional"
15. **Final Verification**

    * `git status` (verify clean working directory)
    * `git log --oneline -5` (verify all commits present)
    * Compare before/after, ensure no regressions
16. **Push Changes**

    * `git push origin <actual-branch-name>`
    * `git status` (verify push succeeded)
17. **Merge Decision** Only proceed if ALL gates pass AND user approves
18. **Close PR** Use GitHub MCP to merge to development, delete feature branch, close PR

    * **VERIFY**: Check merge actually completed
    * `git checkout development`
    * `git pull origin development`
    * `git log --oneline -3` (verify merge commit present)
19. **CRITICAL: Merge Conflict Detection**

    * `git status` (check for "both modified" or conflict indicators)
    * `grep -r "<<<<<<< HEAD" /workspace` (scan for conflict markers)
    * `grep -r "=======" /workspace` (scan for conflict dividers)
    * `grep -r ">>>>>>>" /workspace` (scan for conflict endings)
    * **If conflicts found**: STOP and resolve conflicts manually
    * **Never proceed** with conflict markers in files
20. \*\*Post-Merge Verification

    * `git diff HEAD~1` (verify clean merge, no unexpected changes)
    * Build and test merged code
    * **If merge corrupted code**: Revert merge immediately

EXIT: `PR_REVIEW_COMPLETE` + summary of changes + test results + quality metrics

─────────────────────── GITHUB ISSUES WORKFLOW Repository: [https://github.com/BerryKuipers/TuneScout](https://github.com/BerryKuipers/TuneScout)

**MCP TOOL USAGE:**

* **Local Git Operations**: Use git MCP tools for branch management
* **GitHub Issues API**: Use GitHub MCP tools for issue CRUD operations
* **Integration**: Link issues to PRs, branches, and commits

## ISSUE PICKUP WORKFLOW

Command: `PICKUP_ISSUE <issue_number>` or `PICKUP_ISSUE <issue_url>`

FLOW:

1. **Fetch Issue Details** Use **GitHub MCP** to get issue information

   * Issue title, description, labels, assignees
   * Related issues, linked PRs, comments
   * Priority assessment from labels/milestones

2. **Create Feature Branch** Use **git MCP tools**:

   * **Branch naming**: `issue-<number>-<short-description>` (e.g., `issue-42-fix-authentication-bug`)
   * **Base branch**: Always create from `development`
   * **Steps**:

     ```bash
     git checkout development
     git pull origin development
     git checkout -b issue-<number>-<short-description>
     git push -u origin issue-<number>-<short-description>
     ```

3. **Issue Analysis & Planning**

   * Break down issue into actionable tasks
   * Identify affected services/components
   * Assess complexity and dependencies
   * Plan testing strategy

4. **Implementation Workflow**

   * Apply quality gates and Docker rebuild protocols
   * Follow SOLID/DRY principles
   * Create comprehensive tests
   * Document changes and decisions

5. **Progress Updates**

   * Comment on issue with progress updates
   * Reference commits: "Progress on #42: implemented authentication fix in commit abc123"
   * Update issue labels (in-progress, needs-testing, etc.)

6. **Completion & PR Creation**

   * Push final changes to feature branch
   * Create PR linking to issue: "Fixes #42" or "Closes #42"
   * Request review and testing
   * Merge to development when approved

## ISSUE CREATION WORKFLOW

Command: `CREATE_ISSUE <title>` or automatic detection during development

**Automatic Issue Detection Triggers:**

* Security vulnerabilities found during scanning
* Performance bottlenecks identified
* Code quality violations (critical/high priority)
* Missing error handling or edge cases
* Architecture improvements needed
* Technical debt accumulation
* Dependencies requiring updates

**Issue Creation Process:**

1. **Issue Assessment**

   * Severity: Critical/High/Medium/Low
   * Type: Bug/Enhancement/Task/Documentation
   * Affected components/services
   * Estimated effort and complexity

2. **Issue Template Population**

   ```markdown
   ## Description
   [Clear description of the issue/enhancement]

   ## Affected Components
   - [ ] Service: [service_name]
   - [ ] File: [file_path]
   - [ ] Function: [function_name]

   ## Steps to Reproduce (for bugs)
   1. [Step 1]
   2. [Step 2]
   3. [Expected vs Actual behavior]

   ## Acceptance Criteria
   - [ ] [Criteria 1]
   - [ ] [Criteria 2]
   - [ ] Tests added/updated
   - [ ] Documentation updated

   ## Technical Details
   - Priority: [Critical/High/Medium/Low]
   - Estimated effort: [hours/days]
   - Dependencies: [related issues/PRs]
   ```

3. **Label Assignment**

   * `bug`, `enhancement`, `task`, `documentation`
   * `priority: critical/high/medium/low`
   * `component: orchestrator/ui/api/infrastructure`
   * `effort: small/medium/large`

4. **Project Assignment**

   * Assign to appropriate milestone
   * Add to project board if available
   * Set assignee (self or team member)

## ISSUE MANAGEMENT OPERATIONS

**Check Issue Status:**

```bash
ISSUE_STATUS <issue_number>
# Returns: status, assignee, labels, last updated, linked PRs
```

**Update Issue:**

```bash
UPDATE_ISSUE <issue_number>
# Options: add comment, change labels, update assignee, modify description
```

**Link Issue to Work:**

```bash
LINK_ISSUE <issue_number> <pr_number>
# Cross-reference issue with PR for tracking
```

**Bulk Issue Management:**

```bash
LIST_ISSUES [filter]
# Options: open/closed, assigned to me, by label, by milestone
```

## CONTEXT SWITCHING PROTOCOL

**When Working on Feature Branch but Issue Discovered:**

1. **Immediate Issue Creation**

   ```bash
   CREATE_ISSUE "Critical: Memory leak in voice_io_service"
   LABELS: ["bug", "priority: high", "component: voice-io"]
   DESCRIPTION: [detailed technical description]
   ```

2. **Context Preservation**

   * Commit current work with clear WIP message
   * Document current state in issue or branch
   * Note any temporary workarounds applied

3. **Decision Matrix**

   * **Critical/Blocking**: Switch immediately to fix
   * **High Priority**: Finish current feature, then tackle
   * **Medium/Low**: Add to backlog for future pickup

4. **Branch Management**

   * Keep feature branch active
   * Create separate issue branch if switching context
   * Use git stash/commit for clean context switches

## ISSUE LIFECYCLE TRACKING

**Status Progression:**

```
Open → In Progress → In Review → Testing → Closed
  ↓         ↓            ↓         ↓        ↓
Created   Assigned    PR Created  Testing  Merged
```

**Automated Updates:**

* **Branch Creation**: Comment "Working on this issue in branch issue-X-description"
* **PR Creation**: Automatically link with "Fixes #X" in PR description
* **Commit References**: Include "#X" in commit messages for traceability
* **Testing**: Update with test results and validation steps
* **Completion**: Close issue with summary of changes made

## INTEGRATION WITH EXISTING WORKFLOWS

**PR Review Integration:**

* PRs automatically link to issues via "Fixes #X" keywords
* Issue status updates when PR is merged
* Cross-reference between issue discussions and PR reviews

**Quality Gates Integration:**

* Issues created for quality violations found during scans
* Link quality metrics to specific issues
* Track technical debt reduction through issue resolution

**Memory System Integration:**

* Store issue progress in project memory
* Track patterns of issues for proactive prevention
* Link issues to architectural decisions and changes

## ISSUE COMMANDS REFERENCE

**Basic Operations:**

```bash
PICKUP_ISSUE 42                    # Start working on issue #42
CREATE_ISSUE "Fix authentication"  # Create new issue
UPDATE_ISSUE 42 --status progress # Update issue status
CLOSE_ISSUE 42 --resolution fixed # Close completed issue
```

**Advanced Operations:**

```bash
LIST_MY_ISSUES                     # Show issues assigned to me
FIND_ISSUES --label bug --priority high  # Filter issues
LINK_ISSUES 42 45                  # Link related issues
BULK_UPDATE --label "needs-testing" --add-assignee @username
```

**Integration Commands:**

```bash
ISSUE_TO_BRANCH 42                 # Create branch from issue
BRANCH_TO_PR issue-42-fix-auth     # Create PR from issue branch
SYNC_ISSUE_STATUS                  # Update all issue statuses
```

## QUALITY ASSURANCE FOR ISSUES

**Issue Creation Quality Gates:**

* Clear, actionable description
* Proper labeling and categorization
* Acceptance criteria defined
* Technical details sufficient for implementation

**Issue Resolution Quality Gates:**

* All acceptance criteria met
* Tests added/updated as needed
* Documentation updated
* No regressions introduced
* Performance impact assessed

**Issue Tracking Metrics:**

* Time to resolution by priority level
* Issue recurrence patterns
* Component-specific issue frequency
* Technical debt reduction progress

EXIT CONDITIONS:

* `ISSUE_PICKUP_COMPLETE` + summary of implementation + testing results
* `ISSUE_CREATED` + issue number + tracking URL + priority assessment
* `ISSUE_UPDATED` + status change + next actions + timeline

**CRITICAL SAFETY CHECKS:**

* Verify issue exists before pickup
* Confirm branch creation from correct base (development)
* Validate issue closure only after thorough testing
* Ensure proper cross-referencing between issues, branches, and PRs

\*\*MANDATORY FINAL VERIFICATION:

* `git branch --show-current` (MUST show "development")
* `git status` (MUST show clean working directory on development)
* `git log --oneline -3` (verify merge commit present in development)
* **CRITICAL**: PR workflow is NOT complete until development branch has been merged and pushed

**READY** — Await project root path OR PR\_REVIEW command.
