services:
  tunescout:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "1975:8000"
      - "7861:7860"
    volumes:
      - discogs-data:/app/data
    environment:
      - DISCOGS_TOKEN=${DISCOGS_TOKEN:-}
      - DISCOGS_CONSUMER_KEY=${DISCOGS_CONSUMER_KEY:-}
      - DISCOGS_CONSUMER_SECRET=${DISCOGS_CONSUMER_SECRET:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - LLM_MODEL=${LLM_MODEL:-gpt-4o-mini}
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - FLASK_PORT=8000
      - GRADIO_SERVER_PORT=7860

  frontend-next:
    build:
      context: ./frontend-next
      dockerfile: Dockerfile
    ports:
      - "1976:3000"
    environment:
      - NEXT_PUBLIC_API_BASE=http://tunescout:8000
    depends_on:
      - tunescout
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  discogs-data:
    # This will create a named volume for persistent data storage
