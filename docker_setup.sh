#!/bin/bash

# Add a health endpoint to the Flask application
cat > /home/<USER>/discogs_app/src/routes/health.py << 'EOF'
from flask import Blueprint, jsonify

health_bp = Blueprint('health', __name__)

@health_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Docker healthcheck."""
    return jsonify({"status": "healthy"}), 200
EOF

# Update main.py to include the health endpoint
sed -i '/from src.routes.user import user_bp/a from src.routes.health import health_bp' /home/<USER>/discogs_app/src/main.py
sed -i '/app.register_blueprint(user_bp)/a app.register_blueprint(health_bp)' /home/<USER>/discogs_app/src/main.py

# Update main.py to listen on 0.0.0.0 for Docker
sed -i 's/app.run(debug=True)/app.run(host="0.0.0.0", port=8000, debug=False)/' /home/<USER>/discogs_app/src/main.py

echo "Health endpoint added and Flask configured to listen on all interfaces"
