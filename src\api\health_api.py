"""
Health API endpoints with Swagger documentation
"""

from flask_restx import Resource, Namespace
from src.api import health_model
from datetime import datetime

# Create namespace for health operations
health_ns = Namespace('health', description='Health check operations')

@health_ns.route('/health')
class HealthResource(Resource):
    @health_ns.doc('health_check')
    @health_ns.marshal_with(health_model)
    def get(self):
        """Health check endpoint for monitoring"""
        return {
            'status': 'healthy',
            'app': 'TuneScout',
            'version': '1.0.0',
            'timestamp': datetime.utcnow()
        }
