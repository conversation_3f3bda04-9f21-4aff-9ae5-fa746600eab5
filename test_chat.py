#!/usr/bin/env python3
"""Quick chat test"""
import requests

def test_chat():
    try:
        # Test health first
        health = requests.get("http://127.0.0.1:8000/health", timeout=5)
        print(f"Health: {health.status_code} - {health.json()}")
        
        # Test chat at port 7860 (Gradio)
        chat_response = requests.get("http://127.0.0.1:7860", timeout=5)
        print(f"Chat interface: {chat_response.status_code}")
        
        if chat_response.status_code == 200:
            print("PASS: Chat interface accessible")
        else:
            print("FAIL: Chat interface failed")
            
    except Exception as e:
        print(f"FAIL: Test failed: {e}")

if __name__ == "__main__":
    test_chat()
