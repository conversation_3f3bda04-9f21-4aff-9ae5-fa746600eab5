class SearchInterface {
    constructor() {
        this.searchForm = document.querySelector('#search-form');
        this.searchInput = document.querySelector('#search-input');
        this.resultsContainer = document.querySelector('#results-container');
        this.filtersContainer = document.querySelector('#filters-container');
        this.currentQuery = '';
        this.currentFilters = {};
        this.initializeEventListeners();
        this.loadFilters();
    }

    initializeEventListeners() {
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });
    }

    async loadFilters() {
        try {
            const response = await fetch('/api/filters');
            const filters = await response.json();
            this.renderFilters(filters);
        } catch (error) {
            console.error('Error loading filters:', error);
        }
    }

    renderFilters(filters) {
        this.filtersContainer.innerHTML = `
            <div class="filter-group">
                <label>Type:</label>
                <select id="type-filter">
                    <option value="">All</option>
                    ${filters.types.map(type => `<option value="${type}">${type}</option>`).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label>Genre:</label>
                <select id="genre-filter">
                    <option value="">All</option>
                    ${filters.genres.map(genre => `<option value="${genre}">${genre}</option>`).join('')}
                </select>
            </div>
        `;
    }

    async performSearch() {
        const query = this.searchInput.value.trim();
        if (!query) return;

        this.currentQuery = query;
        this.gatherFilters();
        
        try {
            const params = new URLSearchParams({
                q: query,
                type: this.currentFilters.type || 'release',
                genre: this.currentFilters.genre || '',
                page: 1,
                per_page: 20
            });

            const response = await fetch(`/api/search?${params}`);
            const data = await response.json();
            
            if (data.error) {
                this.showError(data.error);
                return;
            }

            this.renderResults(data.results);
        } catch (error) {
            this.showError('Search failed. Please try again.');
            console.error('Search error:', error);
        }
    }

    gatherFilters() {
        this.currentFilters = {
            type: document.querySelector('#type-filter')?.value || '',
            genre: document.querySelector('#genre-filter')?.value || ''
        };
    }

    renderResults(results) {
        if (!results || results.length === 0) {
            this.resultsContainer.innerHTML = '<p class="text-gray-500 text-center py-8">No results found</p>';
            return;
        }

        this.resultsContainer.innerHTML = results.map(result => this.createResultCard(result)).join('');
        this.attachTrackingListeners();
    }

    createResultCard(result) {
        const imageUrl = result.image_url || result.thumb || '/static/images/placeholder-album.png';
        const artist = result.artist || (result.artists && result.artists[0]?.name) || 'Unknown Artist';
        
        return `
            <div class="result-card bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow"
                 data-tracking-id="${result.tracking_id}" data-result-id="${result.id}" data-result-type="${result.type}">
                <div class="flex items-start space-x-4">
                    <img src="${imageUrl}" alt="${result.title}" 
                         class="w-16 h-16 object-cover rounded-md flex-shrink-0"
                         onerror="this.src='/static/images/placeholder-album.png'">
                    <div class="flex-grow min-w-0">
                        <h3 class="font-semibold text-gray-800 dark:text-white truncate">${result.title}</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">${artist}</p>
                        <p class="text-gray-500 dark:text-gray-400 text-xs mt-1">${result.year || 'Unknown Year'}</p>
                    </div>
                    <button class="view-details-btn bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Details
                    </button>
                </div>
            </div>
        `;
    }

    attachTrackingListeners() {
        // Track views when cards become visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.trackView(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('.result-card').forEach(card => {
            observer.observe(card);
            
            // Track clicks on details button
            const detailsBtn = card.querySelector('.view-details-btn');
            detailsBtn.addEventListener('click', () => this.trackClick(card, 'details'));
        });
    }

    async trackView(cardElement) {
        const trackingData = {
            tracking_id: cardElement.dataset.trackingId,
            result_id: cardElement.dataset.resultId,
            result_type: cardElement.dataset.resultType
        };

        try {
            await fetch('/api/track-view', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(trackingData)
            });
        } catch (error) {
            console.error('Tracking error:', error);
        }
    }

    async trackClick(cardElement, action) {
        const trackingData = {
            tracking_id: cardElement.dataset.trackingId,
            result_id: cardElement.dataset.resultId,
            result_type: cardElement.dataset.resultType,
            action: action
        };

        try {
            await fetch('/api/track-click', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(trackingData)
            });
        } catch (error) {
            console.error('Tracking error:', error);
        }
    }

    showError(message) {
        this.resultsContainer.innerHTML = `<p class="text-red-500 text-center py-8">${message}</p>`;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SearchInterface();
});
