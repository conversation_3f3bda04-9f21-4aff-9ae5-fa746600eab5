#!/usr/bin/env python3
"""Simple startup test"""
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Test startup step by step"""
    print("=== SIMPLE STARTUP TEST ===")
    
    try:
        print("1. Setting environment...")
        os.environ['TESTING'] = '1'
        
        print("2. Importing main module...")
        from src.main import app, initialize_services
        print("✅ Main module imported")
        
        print("3. Initializing services...")
        initialize_services()
        print("✅ Services initialized")
        
        print("4. Testing basic routes...")
        with app.test_client() as client:
            # Test health endpoint
            response = client.get('/health')
            print(f"   Health endpoint: {response.status_code}")
            
            # Test docs redirect
            response = client.get('/docs')
            print(f"   Docs redirect: {response.status_code}")
            if response.status_code == 302:
                print(f"   Redirects to: {response.location}")
            
            # Test API health
            response = client.get('/api/health')
            print(f"   API health: {response.status_code}")
            
            # Test Swagger UI
            response = client.get('/api/docs/')
            print(f"   Swagger UI: {response.status_code}")
        
        print("✅ All basic tests passed!")
        
        print("\n5. Starting Flask app on port 5000...")
        app.run(host="127.0.0.1", port=5000, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
