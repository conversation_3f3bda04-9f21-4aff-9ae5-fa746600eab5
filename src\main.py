"""
Main Application Entry Point for TuneScout

This module serves as the entry point for the TuneScout application,
initializing all necessary services and routes.
"""

import sys
import os
import logging
import threading
from dotenv import load_dotenv
from flask import Flask, render_template, jsonify
from flask_cors import CORS

# Load environment variables from .env file
load_dotenv()

# Download required NLTK data before importing services
import nltk
try:
    nltk.data.find('tokenizers/punkt_tab')
    nltk.data.find('corpora/stopwords')
except LookupError:
    logging.info("Downloading required NLTK data...")
    nltk.download('punkt_tab', quiet=True)
    nltk.download('stopwords', quiet=True)
    logging.info("NLTK data download completed")

from src.routes.user import user_bp
from src.routes.health import health_bp
from src.routes.search import search_bp
from src.services.discogs_service import DiscogsService
from src.services.nlp_service import NLPService
from src.services.auth_service import AuthenticationService as AuthService
from src.services.database_service import DatabaseService
from src.services.settings_service import SettingsService
from src.frontend.chat_interface import create_chat_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Initialize Flask app
app = Flask(__name__, template_folder='templates', static_folder='static')
CORS(app)

# Initialize services
db_service = DatabaseService()
settings_service = SettingsService(db_service)
discogs_token = settings_service.get_api_token() or os.environ.get('DISCOGS_TOKEN', '')
discogs_service = DiscogsService(token=discogs_token, user_agent='TuneScout/1.0')
nlp_service = NLPService(settings_service=settings_service)
auth_service = AuthService(db_service)

# Register blueprints
app.register_blueprint(user_bp)
app.register_blueprint(health_bp)
app.register_blueprint(search_bp, url_prefix='/api')

# Create Gradio interface
chat_interface = create_chat_app(
    discogs_service=discogs_service,
    nlp_service=nlp_service,
    auth_service=auth_service,
    settings_service=settings_service
)

def start_gradio():
    chat_interface.launch(server_port=7860, share=False, server_name="0.0.0.0", inbrowser=False, quiet=True)

@app.route('/')
def index():
    """Render the main application page."""
    return render_template('index.html')

@app.route('/api/health')
def health():
    """API health check endpoint."""
    return jsonify({"status": "healthy", "app": "TuneScout"})

if __name__ == '__main__':
    # Gradio disabled - APIs working fine
    # gradio_thread = threading.Thread(target=start_gradio, daemon=True)
    # gradio_thread.start()
    
    # Run Flask app
    app.run(host="0.0.0.0", port=8000, debug=True)
