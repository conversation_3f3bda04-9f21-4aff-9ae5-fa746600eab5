<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TuneScout - AI Music Discovery</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="/static/js/search.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
        }
        .dark-mode {
            background-color: #1f2937;
            color: #f9fafb;
        }
        .chat-container {
            height: calc(100vh - 200px);
        }
        .message-user {
            background-color: #e5e7eb;
            border-radius: 18px 18px 0 18px;
        }
        .message-bot {
            background-color: #3b82f6;
            color: white;
            border-radius: 18px 18px 18px 0;
        }
        .dark-mode .message-user {
            background-color: #374151;
        }
        .dark-mode .message-bot {
            background-color: #2563eb;
        }
        .album-card {
            transition: transform 0.3s ease;
        }
        .album-card:hover {
            transform: translateY(-5px);
        }
        .theme-toggle {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white dark:bg-gray-800 shadow-md">
            <div class="container mx-auto px-4 py-4 flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-record-vinyl text-blue-600 text-2xl"></i>
                    <h1 class="text-xl font-bold text-gray-800 dark:text-white">TuneScout</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="theme-toggle p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-moon text-gray-600 dark:text-gray-300"></i>
                    </button>
                    <div class="relative">
                        <button id="user-menu" class="flex items-center space-x-1 focus:outline-none">
                            <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">berry</span>
                            <i class="fas fa-chevron-down text-xs text-gray-500 dark:text-gray-400"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto px-4 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Sidebar -->
                <div class="lg:col-span-1 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                    <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-white">Navigation</h2>
                    <nav>
                        <ul class="space-y-2">
                            <li>
                                <a href="#" class="flex items-center space-x-2 p-2 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                    <i class="fas fa-comments"></i>
                                    <span>Chat</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-search"></i>
                                    <span>Discover</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-heart"></i>
                                    <span>Collection</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-star"></i>
                                    <span>Recommendations</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                            </li>
                        </ul>
                    </nav>

                    <div class="mt-8">
                        <h3 class="text-md font-semibold mb-3 text-gray-800 dark:text-white">Recent Searches</h3>
                        <div class="space-y-2">
                            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300">
                                <p>Radiohead albums from the 90s</p>
                            </div>
                            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300">
                                <p>Jazz releases from Blue Note</p>
                            </div>
                            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm text-gray-700 dark:text-gray-300">
                                <p>Latest electronic music</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Area -->
                <div class="lg:col-span-3 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 flex flex-col">
                    <!-- Search Form -->
                    <div class="border-b dark:border-gray-700 pb-4 mb-4">
                        <form id="search-form" class="flex items-center space-x-4">
                            <input 
                                id="search-input" 
                                type="text" 
                                placeholder="Search for music..." 
                                class="flex-grow p-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </form>
                        
                        <!-- Filters -->
                        <div id="filters-container" class="flex items-center space-x-4 mt-4">
                            <!-- Filters loaded dynamically -->
                        </div>
                    </div>

                    <!-- Results -->
                    <div id="results-container" class="flex-grow space-y-4 overflow-y-auto">
                        <div class="text-center py-12">
                            <i class="fas fa-search text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">Start searching to discover music!</p>
                            <p class="text-gray-400 text-sm mt-2">Try: "Radiohead", "90s rock", "Blue Note jazz"</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white dark:bg-gray-800 shadow-inner mt-6">
            <div class="container mx-auto px-4 py-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <p>© 2025 TuneScout. Powered by Discogs API.</p>
                    </div>
                    <div class="flex space-x-4 mt-2 md:mt-0">
                        <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
            htmlElement.classList.add('dark-mode');
            themeToggle.innerHTML = '<i class="fas fa-sun text-yellow-400"></i>';
        }
        
        themeToggle.addEventListener('click', () => {
            htmlElement.classList.toggle('dark-mode');
            
            if (htmlElement.classList.contains('dark-mode')) {
                localStorage.setItem('theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun text-yellow-400"></i>';
            } else {
                localStorage.setItem('theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon text-gray-600"></i>';
            }
        });
    </script>
</body>
</html>
