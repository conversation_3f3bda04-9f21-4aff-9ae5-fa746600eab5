import time
from collections import defaultdict, Counter
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class AnalyticsService:
    """Track user interactions with search results."""
    
    def __init__(self):
        self.searches = []
        self.views = []
        self.clicks = []
        self.genres_cache = []
    
    def track_search(self, query: str, search_type: str, genre: str, year: str) -> None:
        """Track search query."""
        self.searches.append({
            'query': query,
            'type': search_type,
            'genre': genre,
            'year': year,
            'timestamp': time.time()
        })
    
    def track_view(self, tracking_id: str, result_id: str, result_type: str) -> None:
        """Track result card view."""
        self.views.append({
            'tracking_id': tracking_id,
            'result_id': result_id,
            'type': result_type,
            'timestamp': time.time()
        })
    
    def track_click(self, tracking_id: str, result_id: str, result_type: str, action: str) -> None:
        """Track result card click."""
        self.clicks.append({
            'tracking_id': tracking_id,
            'result_id': result_id,
            'type': result_type,
            'action': action,
            'timestamp': time.time()
        })
    
    def get_popular_genres(self) -> List[str]:
        """Get popular genres from search history."""
        if self.genres_cache:
            return self.genres_cache
        
        genre_counter = Counter()
        for search in self.searches[-1000:]:  # Last 1000 searches
            if search.get('genre'):
                genre_counter[search['genre']] += 1
        
        popular = [genre for genre, count in genre_counter.most_common(20)]
        
        # Add common genres if not enough data
        common_genres = ['Rock', 'Electronic', 'Jazz', 'Hip Hop', 'Pop', 'Classical', 'Folk', 'Blues']
        for genre in common_genres:
            if genre not in popular:
                popular.append(genre)
        
        self.genres_cache = popular[:20]
        return self.genres_cache
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get analytics summary."""
        return {
            'total_searches': len(self.searches),
            'total_views': len(self.views),
            'total_clicks': len(self.clicks),
            'view_to_click_ratio': len(self.clicks) / max(len(self.views), 1),
            'popular_queries': [s['query'] for s in self.searches[-10:]],
            'popular_genres': self.get_popular_genres()[:10]
        }
