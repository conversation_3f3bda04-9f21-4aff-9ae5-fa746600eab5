#!/usr/bin/env python3
"""
Simple startup script to test the application with Swagger
"""
import os
import sys
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Start the application"""
    try:
        # Set up basic logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        print("Starting TuneScout application with Swagger UI...")
        print("Swagger UI will be available at:")
        print("  - http://localhost:8000/docs (redirect)")
        print("  - http://localhost:8000/api/docs/ (direct)")
        print("=" * 60)
        
        # Import and run the main application
        from src.main import app
        
        # Run the Flask app
        app.run(host="0.0.0.0", port=8000, debug=True, use_reloader=False)
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
