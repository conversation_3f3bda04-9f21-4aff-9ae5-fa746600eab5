#!/usr/bin/env python3
"""Debug application startup"""
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Debug startup step by step"""
    print("=== DEBUG STARTUP ===")
    
    try:
        print("1. Importing main module...")
        from src.main import app
        print("✅ Main module imported successfully")
        
        print("2. Checking app configuration...")
        print(f"   Database URI: {app.config.get('SQLALCHEMY_DATABASE_URI')}")
        print(f"   Blueprints: {list(app.blueprints.keys())}")
        print("✅ App configuration looks good")
        
        print("3. Testing basic route...")
        with app.test_client() as client:
            response = client.get('/health')
            print(f"   Health endpoint: {response.status_code}")
            
            response = client.get('/docs')
            print(f"   Docs redirect: {response.status_code}")
            
            response = client.get('/api/docs/')
            print(f"   Swagger UI: {response.status_code}")
        
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
