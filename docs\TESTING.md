# Testing Guide for TuneScout

This guide explains how to run tests for the TuneScout application.

## Test Structure

All tests are located in the `tests/` directory:

```
tests/
├── run_tests.py              # Main test runner
├── test_app_startup.py       # Application startup tests
├── test_chat_interface.py    # Chat interface and Gradio tests
├── test_swagger.py           # Swagger UI and API documentation tests
├── test_services.py          # Service layer unit tests
├── test_end_to_end.py        # End-to-end integration tests
└── fixtures/                 # Test data and fixtures
```

## Running Tests

### Quick Test Runner

Use the main test runner for all tests:

```bash
# Run all tests
python tests/run_tests.py

# Run specific test categories
python tests/run_tests.py --unit        # Unit tests only
python tests/run_tests.py --startup     # Startup tests only
python tests/run_tests.py --integration # Integration tests only
```

### Individual Test Files

Run specific test files directly:

```bash
# Test application startup and configuration
python tests/test_app_startup.py

# Test Swagger UI (requires running app)
python tests/test_swagger.py

# Test chat interface (requires running app)
python tests/test_chat_interface.py

# Run existing unit tests
python -m pytest tests/test_services.py -v
python -m pytest tests/test_end_to_end.py -v
```

## Test Categories

### 1. Unit Tests
- **File**: `test_services.py`
- **Purpose**: Test individual service components
- **Requirements**: None (mocked dependencies)
- **Run with**: `python -m pytest tests/test_services.py -v`

### 2. Startup Tests
- **File**: `test_app_startup.py`
- **Purpose**: Test application initialization and configuration
- **Requirements**: None (imports only)
- **Run with**: `python tests/test_app_startup.py`

### 3. Integration Tests
- **Files**: `test_swagger.py`, `test_chat_interface.py`
- **Purpose**: Test running application endpoints and interfaces
- **Requirements**: Running application
- **Run with**: Start app first, then run tests

## Testing Swagger UI

The Swagger UI tests verify:
- ✅ API documentation is accessible at `/docs` and `/api/docs/`
- ✅ All API endpoints respond correctly
- ✅ Health checks work
- ✅ Error handling is proper

### Manual Swagger Testing

1. Start the application:
   ```bash
   python -m src.main
   # or
   docker-compose up -d
   ```

2. Open Swagger UI in browser:
   - http://localhost:8000/docs (redirect)
   - http://localhost:8000/api/docs/ (direct)

3. Test API endpoints interactively through the Swagger UI

## Testing Chat Interface

The chat interface tests verify:
- ✅ Gradio interface is accessible
- ✅ Port configuration works correctly
- ✅ Flask and Gradio integration works

## Docker Testing

To test the full Docker setup:

```bash
# Build and start containers
docker-compose up -d --build

# Wait for services to start, then test
python tests/test_swagger.py
python tests/test_chat_interface.py

# Check logs
docker-compose logs -f
```

## Environment Variables for Testing

Set these environment variables for testing:

```bash
# Port configuration
export GRADIO_SERVER_PORT=7860
export FLASK_PORT=8000

# API tokens (for integration tests)
export DISCOGS_TOKEN=your_token_here
export OPENAI_API_KEY=your_key_here
```

## Continuous Integration

For CI/CD pipelines, use:

```bash
# Install dependencies
pip install -r requirements.txt

# Run unit and startup tests (no running app required)
python tests/run_tests.py --unit --startup

# For full integration testing, start app in background:
python -m src.main &
sleep 10  # Wait for startup
python tests/run_tests.py --integration
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Make sure you're in the project root
   cd /path/to/TuneScout
   python tests/test_app_startup.py
   ```

2. **Connection Refused**
   ```bash
   # Make sure the application is running
   python -m src.main
   # Then in another terminal:
   python tests/test_swagger.py
   ```

3. **Port Conflicts**
   ```bash
   # Check what's using the ports
   netstat -an | grep :8000
   netstat -an | grep :7860
   
   # Kill processes if needed (Windows)
   powershell scripts/kill_gradio.ps1
   ```

### Test Dependencies

Make sure these packages are installed:
- `requests` (for HTTP testing)
- `pytest` (optional, for advanced test running)
- `flask-restx` (for Swagger UI)

## Adding New Tests

When adding new features, create corresponding tests:

1. **Unit tests** in `test_services.py` for new services
2. **API tests** in `test_swagger.py` for new endpoints
3. **Integration tests** for new user-facing features

Follow the existing test patterns and use proper assertions and error handling.
