$gradioPids = Get-NetTCPConnection -LocalPort 7860 | Select-Object -ExpandProperty OwningProcess | Where-Object { $_ -ne 0 } 
if ($gradioPids) {
    foreach ($pid in $gradioPids) {
        try {
            Stop-Process -Id $pid -Force
            Write-Output "Killed process with PID $pid on port 7860."
        } catch {
            $err = $_
            Write-Output ("Failed to kill process with PID {0}: {1}" -f $pid, $err.Exception.Message)
        }
    }
} else {
    Write-Output "No process found using port 7860."
} 