"""
Settings Management Service

This module provides functionality for managing API keys and user preferences.
"""

import os
import logging
import json
from typing import Dict, Any, Optional, Union
from src.services.database_service import DatabaseService

logger = logging.getLogger(__name__)

class SettingsService:
    """Service for managing application settings and user preferences."""
    
    # Default settings values
    DEFAULT_SETTINGS = {
        'theme': 'light',
        'items_per_page': 10,
        'email_notifications': False,
        'share_collection': False,
        'share_wantlist': False,
        'discogs_token': ''
    }
    
    def __init__(self, db_service: Optional[DatabaseService] = None):
        """
        Initialize the settings service.
        
        Args:
            db_service: Database service for persistent storage
        """
        self.db_service = db_service or DatabaseService()
        self.app_settings = self._load_app_settings()
    
    def _load_app_settings(self) -> Dict[str, Any]:
        """Load application settings from file."""
        settings_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'app_settings.json')
        os.makedirs(os.path.dirname(settings_path), exist_ok=True)
        
        if not os.path.exists(settings_path):
            # Create default settings
            default_settings = {
                'discogs_api': {
                    'token': os.environ.get('DISCOGS_TOKEN', ''),
                    'request_token_url': 'https://api.discogs.com/oauth/request_token',
                    'authorize_url': 'https://www.discogs.com/oauth/authorize',
                    'access_token_url': 'https://api.discogs.com/oauth/access_token',
                    'user_agent': 'TuneScout/1.0',
                    'rate_limit': 60,  # requests per minute
                    'rate_limit_unauthenticated': 25,  # requests per minute for unauthenticated requests
                },
                'app': {
                    'default_theme': 'light',
                    'default_items_per_page': 10,
                    'max_chat_history': 100,
                    'enable_analytics': False,
                },
                'ai': {
                    'model': 'default',
                    'temperature': 0.7,
                    'max_tokens': 1000,
                    'enable_function_calling': True,
                    'llm_provider': os.environ.get('LLM_PROVIDER', 'openai')
                }
            }
            
            with open(settings_path, 'w') as f:
                json.dump(default_settings, f, indent=2)
            
            return default_settings
        
        try:
            with open(settings_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading app settings: {str(e)}")
            return {}
    
    def save_app_settings(self) -> bool:
        """Save application settings to file."""
        settings_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'app_settings.json')
        
        try:
            with open(settings_path, 'w') as f:
                json.dump(self.app_settings, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving app settings: {str(e)}")
            return False
    
    def get_app_setting(self, category: str, key: str, default: Any = None) -> Any:
        """
        Get an application setting.
        
        Args:
            category: Setting category
            key: Setting key
            default: Default value if setting not found
            
        Returns:
            Setting value
        """
        try:
            return self.app_settings.get(category, {}).get(key, default)
        except Exception as e:
            logger.error(f"Error getting app setting {category}.{key}: {str(e)}")
            return default
    
    def set_app_setting(self, category: str, key: str, value: Any) -> bool:
        """
        Set an application setting.
        
        Args:
            category: Setting category
            key: Setting key
            value: Setting value
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if category not in self.app_settings:
                self.app_settings[category] = {}
            
            self.app_settings[category][key] = value
            return self.save_app_settings()
        except Exception as e:
            logger.error(f"Error setting app setting {category}.{key}: {str(e)}")
            return False
    
    def get_user_settings(self, user_id: int) -> Dict[str, Any]:
        """
        Get settings for a specific user.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary of user settings
        """
        try:
            settings = self.db_service.get_user_settings(user_id)
            
            # Start with default settings
            result = self.DEFAULT_SETTINGS.copy()
            
            if settings:
                # Update with actual settings where available
                for key in result:
                    if key in settings and settings[key] is not None:
                        result[key] = settings[key]
            else:
                # Create default settings for user if none exist
                default_settings = {
                    'theme': self.get_app_setting('app', 'default_theme', 'light'),
                    'items_per_page': self.get_app_setting('app', 'default_items_per_page', 10),
                    'email_notifications': False,
                    'share_collection': False,
                    'share_wantlist': False,
                }
                self.db_service.create_user_settings(user_id, default_settings)
            
            return result
        except Exception as e:
            logger.error(f"Error getting user settings for user {user_id}: {str(e)}")
            # Return default settings on error
            return self.DEFAULT_SETTINGS.copy()
    
    def update_user_settings(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update settings for a specific user.
        
        Args:
            user_id: User ID
            updates: Dictionary of setting updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            return self.db_service.update_user_settings(user_id, updates)
        except Exception as e:
            logger.error(f"Error updating user settings for user {user_id}: {str(e)}")
            return False
    
    def get_api_token(self, user_id: Optional[int] = None) -> str:
        """
        Get Discogs API token for the application or a specific user.
        
        Args:
            user_id: Optional user ID for user-specific token
            
        Returns:
            Discogs API token
        """
        if user_id:
            # Get user-specific API token
            settings = self.get_user_settings(user_id)
            if settings and settings['discogs_token']:
                return settings['discogs_token']
        
        # Fall back to application API token
        return self.get_app_setting('discogs_api', 'token', '')
    
    def set_api_token(self, token: str, user_id: Optional[int] = None) -> bool:
        """
        Set Discogs API token for the application or a specific user.
        
        Args:
            token: Discogs API token
            user_id: Optional user ID for user-specific token
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if user_id:
                # Set user-specific API token
                return self.update_user_settings(
                    user_id,
                    {
                        'discogs_token': token
                    }
                )
            else:
                # Set application API token
                self.set_app_setting('discogs_api', 'token', token)
                return self.save_app_settings()
        except Exception as e:
            logger.error(f"Error setting API token: {str(e)}")
            return False
    
    def get_ai_settings(self) -> Dict[str, Any]:
        """
        Get AI-related settings.
        
        Returns:
            Dictionary of AI settings
        """
        return self.app_settings.get('ai', {})
    
    def update_ai_settings(self, updates: Dict[str, Any]) -> bool:
        """
        Update AI-related settings.
        
        Args:
            updates: Dictionary of setting updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if 'ai' not in self.app_settings:
                self.app_settings['ai'] = {}
            
            self.app_settings['ai'].update(updates)
            return self.save_app_settings()
        except Exception as e:
            logger.error(f"Error updating AI settings: {str(e)}")
            return False
    
    def get_llm_api_key(self, provider: Optional[str] = None) -> Optional[str]:
        """
        Get LLM API key from environment variables based on the provider.
        If no provider is specified, it tries to get based on the app settings.
        """
        if provider is None:
            provider = self.get_app_setting('ai', 'llm_provider', 'openai')

        if provider == 'openai':
            return os.environ.get('OPENAI_API_KEY')
        elif provider == 'anthropic':
            return os.environ.get('ANTHROPIC_API_KEY')
        # Add other providers as needed
        else:
            logger.warning(f"Unsupported LLM provider: {provider}")
            return None
