{"name": "sharp", "description": "High performance Node.js image processing, the fastest module to resize JPEG, PNG, WebP, GIF, AVIF and TIFF images", "version": "0.34.2", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://sharp.pixelplumbing.com", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <jona<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <juliano<PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Amit <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Alice Monday <<EMAIL>>", "<PERSON><PERSON> <kristo.j<PERSON><PERSON><PERSON>@gmail.com>", "YvesBos <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Jarda Kotěšovec <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <nathanrg<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON>yl<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Freezy <<EMAIL>>", "Daiz <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> <**************>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "alza54 <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <micha<PERSON>@nutt.im>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "scripts": {"install": "node install/check", "clean": "rm -rf src/build/ .nyc_output/ coverage/ test/fixtures/output.*", "test": "npm run test-lint && npm run test-unit && npm run test-licensing && npm run test-types", "test-lint": "semistandard && cpplint", "test-unit": "nyc --reporter=lcov --reporter=text --check-coverage --branches=100 mocha", "test-licensing": "license-checker --production --summary --onlyAllow=\"Apache-2.0;BSD;ISC;LGPL-3.0-or-later;MIT\"", "test-leak": "./test/leak/leak.sh", "test-types": "tsd", "package-from-local-build": "node npm/from-local-build", "package-from-github-release": "node npm/from-github-release", "docs-build": "node docs/build.mjs", "docs-serve": "cd docs && npm start", "docs-publish": "cd docs && npm run build && npx firebase-tools deploy --project pixelplumbing --only hosting:pixelplumbing-sharp"}, "type": "commonjs", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["install", "lib", "src/*.{cc,h,gyp}"], "repository": {"type": "git", "url": "git://github.com/lovell/sharp.git"}, "keywords": ["jpeg", "png", "webp", "avif", "tiff", "gif", "svg", "jp2", "dzi", "image", "resize", "thumbnail", "crop", "embed", "libvips", "vips"], "dependencies": {"color": "^4.2.3", "detect-libc": "^2.0.4", "semver": "^7.7.2"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "0.34.2", "@img/sharp-darwin-x64": "0.34.2", "@img/sharp-libvips-darwin-arm64": "1.1.0", "@img/sharp-libvips-darwin-x64": "1.1.0", "@img/sharp-libvips-linux-arm": "1.1.0", "@img/sharp-libvips-linux-arm64": "1.1.0", "@img/sharp-libvips-linux-ppc64": "1.1.0", "@img/sharp-libvips-linux-s390x": "1.1.0", "@img/sharp-libvips-linux-x64": "1.1.0", "@img/sharp-libvips-linuxmusl-arm64": "1.1.0", "@img/sharp-libvips-linuxmusl-x64": "1.1.0", "@img/sharp-linux-arm": "0.34.2", "@img/sharp-linux-arm64": "0.34.2", "@img/sharp-linux-s390x": "0.34.2", "@img/sharp-linux-x64": "0.34.2", "@img/sharp-linuxmusl-arm64": "0.34.2", "@img/sharp-linuxmusl-x64": "0.34.2", "@img/sharp-wasm32": "0.34.2", "@img/sharp-win32-arm64": "0.34.2", "@img/sharp-win32-ia32": "0.34.2", "@img/sharp-win32-x64": "0.34.2"}, "devDependencies": {"@emnapi/runtime": "^1.4.3", "@img/sharp-libvips-dev": "1.1.0", "@img/sharp-libvips-dev-wasm32": "1.1.0", "@img/sharp-libvips-win32-arm64": "1.1.0", "@img/sharp-libvips-win32-ia32": "1.1.0", "@img/sharp-libvips-win32-x64": "1.1.0", "@types/node": "*", "cc": "^3.0.1", "emnapi": "^1.4.3", "exif-reader": "^2.0.2", "extract-zip": "^2.0.1", "icc": "^3.0.0", "jsdoc-to-markdown": "^9.1.1", "license-checker": "^25.0.1", "mocha": "^11.4.0", "node-addon-api": "^8.3.1", "nyc": "^17.1.0", "prebuild": "^13.0.1", "semistandard": "^17.0.0", "tar-fs": "^3.0.8", "tsd": "^0.32.0"}, "license": "Apache-2.0", "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "config": {"libvips": ">=8.16.1"}, "funding": {"url": "https://opencollective.com/libvips"}, "binary": {"napi_versions": [9]}, "semistandard": {"env": ["mocha"]}, "cc": {"linelength": "120", "filter": ["build/include"]}, "nyc": {"include": ["lib"]}, "tsd": {"directory": "test/types/"}}