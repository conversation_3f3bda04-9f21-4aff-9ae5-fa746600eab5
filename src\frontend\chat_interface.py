import gradio as gr
import logging
from src.services.discogs_service import DiscogsService
from src.services.nlp_service import NLPService
from src.services.settings_service import SettingsService
from src.services.auth_service import AuthenticationService as AuthService

logger = logging.getLogger(__name__)

def create_chat_app(discogs_service, nlp_service, auth_service, settings_service):
    """Create chat interface with real Discogs API integration."""
    
    def chat_fn(message, history):
        """Chat function with real Discogs API integration."""
        if not message.strip():
            return history, ""
        
        try:
            # Use NLP service to process the message and integrate with Discogs API
            response = nlp_service.process_message(message, discogs_service)
            
            # Add to history in messages format
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": response})
            
        except Exception as e:
            logger.error(f"Chat error: {e}")
            error_msg = f"Sorry, I encountered an error while processing your request: {str(e)}"
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": error_msg})
            
        return history, ""
    
    def clear_fn():
        """Clear chat history."""
        return [], ""
    
    # Create interface with responsive layout
    with gr.Blocks(title="AI Discogs Assistant", css="""
        .main-container { max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: 1fr 300px; gap: 20px; }
        .chat-panel { max-width: 800px; }
        .sidebar-panel { background: #1a1a1a; border-radius: 8px; padding: 16px; }
        .chatbot { height: 500px !important; }
        @media (max-width: 768px) { 
            .main-container { grid-template-columns: 1fr; }
            .sidebar-panel { display: none; }
        }
    """) as app:
        with gr.Row(elem_classes=["main-container"]):
            with gr.Column(elem_classes=["chat-panel"]):
                gr.Markdown("# AI Discogs Assistant")
                
                chatbot = gr.Chatbot(label="Chat History", height=500, type="messages", elem_classes=["chatbot"])
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="Message", 
                        placeholder="Ask about music, artists, albums...",
                        scale=4
                    )
                    send_btn = gr.Button("Send", scale=1)
                
                clear_btn = gr.Button("Clear Chat")
            
            with gr.Column(elem_classes=["sidebar-panel"]):
                gr.Markdown("### Quick Actions")
                gr.Markdown("🎵 Search artists\n🎧 Find albums\n📀 Browse by genre")
                gr.Markdown("### Recent Searches")
                gr.Markdown("• The Beatles\n• OK Computer\n• Radiohead")
        
        # Event binding without queue
        send_btn.click(chat_fn, inputs=[msg, chatbot], outputs=[chatbot, msg], queue=False)
        msg.submit(chat_fn, inputs=[msg, chatbot], outputs=[chatbot, msg], queue=False)
        clear_btn.click(clear_fn, inputs=[], outputs=[chatbot, msg], queue=False)
    
    # Disable queue after creation
    app.queue(max_size=None)
    
    return app

class ChatInterface:
    def __init__(self, discogs_service, nlp_service, auth_service, settings_service):
        self.app = create_chat_app(discogs_service, nlp_service, auth_service, settings_service)
    
    def launch(self, **kwargs):
        self.app.launch(**kwargs)
