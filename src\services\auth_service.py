"""
User Authentication and Profile Management

This module provides functionality for user authentication and profile management
using OAuth with Discogs.
"""

import os
import logging
import time
import json
from typing import Dict, Any, Optional, Tuple
import requests
from urllib.parse import urlencode, parse_qs

logger = logging.getLogger(__name__)

class AuthenticationService:
    """Service for handling user authentication with Discogs OAuth."""
    
    # Discogs OAuth endpoints
    REQUEST_TOKEN_URL = 'https://api.discogs.com/oauth/request_token'
    AUTHORIZE_URL = 'https://www.discogs.com/oauth/authorize'
    ACCESS_TOKEN_URL = 'https://api.discogs.com/oauth/access_token'
    
    def __init__(self, consumer_key: Optional[str] = None, consumer_secret: Optional[str] = None, db_service: Optional[Any] = None):
        """
        Initialize the authentication service.
        
        Args:
            consumer_key: Discogs API consumer key
            consumer_secret: Discogs API consumer secret
            db_service: DatabaseService instance (if needed for storing tokens, etc.)
        """
        self.consumer_key = consumer_key or os.environ.get('DISCOGS_CONSUMER_KEY')
        self.consumer_secret = consumer_secret or os.environ.get('DISCOGS_CONSUMER_SECRET')
        self.db_service = db_service

        logger.info(f"Attempting to load DISCOGS_CONSUMER_KEY: {bool(self.consumer_key)}")
        logger.info(f"Attempting to load DISCOGS_CONSUMER_SECRET: {bool(self.consumer_secret)}")

        # if not self.consumer_key or not self.consumer_secret: # Temporarily commenting out the warning
        #     logger.warning("Discogs consumer key or secret not provided. OAuth authentication will not work.")
    
    def get_request_token(self, callback_url: str) -> Dict[str, str]:
        """
        Get OAuth request token from Discogs.
        
        Args:
            callback_url: URL to redirect to after authorization
            
        Returns:
            Dictionary containing request token and secret
        """
        try:
            # Create authorization header
            auth_header = self._create_auth_header(
                'GET', 
                self.REQUEST_TOKEN_URL,
                {'oauth_callback': callback_url}
            )
            
            # Make request
            response = requests.get(
                self.REQUEST_TOKEN_URL,
                headers={'Authorization': auth_header}
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to get request token: {response.text}")
                raise ValueError(f"Failed to get request token: {response.status_code}")
            
            # Parse response
            token_data = parse_qs(response.text)
            return {
                'oauth_token': token_data.get('oauth_token', [''])[0],
                'oauth_token_secret': token_data.get('oauth_token_secret', [''])[0]
            }
        except Exception as e:
            logger.error(f"Error getting request token: {str(e)}")
            raise
    
    def get_authorization_url(self, oauth_token: str) -> str:
        """
        Get the authorization URL for the user to approve access.
        
        Args:
            oauth_token: OAuth request token
            
        Returns:
            Authorization URL
        """
        return f"{self.AUTHORIZE_URL}?oauth_token={oauth_token}"
    
    def get_access_token(self, oauth_token: str, oauth_token_secret: str, oauth_verifier: str) -> Dict[str, str]:
        """
        Exchange request token for access token.
        
        Args:
            oauth_token: OAuth request token
            oauth_token_secret: OAuth request token secret
            oauth_verifier: OAuth verifier code
            
        Returns:
            Dictionary containing access token and secret
        """
        try:
            # Create authorization header
            auth_header = self._create_auth_header(
                'POST', 
                self.ACCESS_TOKEN_URL,
                {
                    'oauth_token': oauth_token,
                    'oauth_verifier': oauth_verifier
                },
                oauth_token_secret
            )
            
            # Make request
            response = requests.post(
                self.ACCESS_TOKEN_URL,
                headers={'Authorization': auth_header}
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to get access token: {response.text}")
                raise ValueError(f"Failed to get access token: {response.status_code}")
            
            # Parse response
            token_data = parse_qs(response.text)
            return {
                'oauth_token': token_data.get('oauth_token', [''])[0],
                'oauth_token_secret': token_data.get('oauth_token_secret', [''])[0]
            }
        except Exception as e:
            logger.error(f"Error getting access token: {str(e)}")
            raise
    
    def _create_auth_header(self, method: str, url: str, params: Dict[str, str], 
                           token_secret: str = '') -> str:
        """
        Create OAuth authorization header.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: Request URL
            params: OAuth parameters
            token_secret: OAuth token secret (if available)
            
        Returns:
            Authorization header string
        """
        # Add required OAuth parameters
        oauth_params = {
            'oauth_consumer_key': self.consumer_key,
            'oauth_nonce': str(int(time.time() * 1000)),
            'oauth_signature_method': 'PLAINTEXT',
            'oauth_timestamp': str(int(time.time())),
            'oauth_version': '1.0'
        }
        
        # Add provided parameters
        oauth_params.update(params)
        
        # Create signature
        signature = f"{self.consumer_secret}&{token_secret}"
        oauth_params['oauth_signature'] = signature
        
        # Format header
        auth_header = 'OAuth ' + ', '.join([f'{k}="{v}"' for k, v in oauth_params.items()])
        
        return auth_header


class UserProfileService:
    """Service for managing user profiles."""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the user profile service.
        
        Args:
            db_path: Path to the database file
        """
        self.db_path = db_path or os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'profiles.json')
        self._ensure_db_exists()
    
    def _ensure_db_exists(self) -> None:
        """Ensure the database file exists."""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        if not os.path.exists(self.db_path):
            with open(self.db_path, 'w') as f:
                json.dump({}, f)
    
    def get_profile(self, username: str) -> Dict[str, Any]:
        """
        Get a user profile by username.
        
        Args:
            username: Username to look up
            
        Returns:
            User profile data
        """
        try:
            with open(self.db_path, 'r') as f:
                profiles = json.load(f)
            
            return profiles.get(username, {'username': username})
        except Exception as e:
            logger.error(f"Error getting profile for {username}: {str(e)}")
            return {'username': username, 'error': str(e)}
    
    def save_profile(self, username: str, profile_data: Dict[str, Any]) -> bool:
        """
        Save a user profile.
        
        Args:
            username: Username to save profile for
            profile_data: Profile data to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.db_path, 'r') as f:
                profiles = json.load(f)
            
            profiles[username] = profile_data
            
            with open(self.db_path, 'w') as f:
                json.dump(profiles, f)
            
            return True
        except Exception as e:
            logger.error(f"Error saving profile for {username}: {str(e)}")
            return False
    
    def update_profile(self, username: str, updates: Dict[str, Any]) -> bool:
        """
        Update a user profile.
        
        Args:
            username: Username to update profile for
            updates: Profile data updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            profile = self.get_profile(username)
            profile.update(updates)
            return self.save_profile(username, profile)
        except Exception as e:
            logger.error(f"Error updating profile for {username}: {str(e)}")
            return False
    
    def delete_profile(self, username: str) -> bool:
        """
        Delete a user profile.
        
        Args:
            username: Username to delete profile for
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.db_path, 'r') as f:
                profiles = json.load(f)
            
            if username in profiles:
                del profiles[username]
                
                with open(self.db_path, 'w') as f:
                    json.dump(profiles, f)
                
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error deleting profile for {username}: {str(e)}")
            return False
    
    def get_all_profiles(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all user profiles.
        
        Returns:
            Dictionary of all user profiles
        """
        try:
            with open(self.db_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error getting all profiles: {str(e)}")
            return {}
