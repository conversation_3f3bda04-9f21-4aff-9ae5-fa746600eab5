"""
User API endpoints with Swagger documentation
"""

from flask import request
from flask_restx import Resource, Namespace, fields
from src.models.user import User, db
from src.api import user_model, error_model
import logging

logger = logging.getLogger(__name__)

# Create namespace for user operations
user_ns = Namespace('users', description='User management operations')

# Create user input model for POST requests
create_user_model = user_ns.model('CreateUser', {
    'username': fields.String(required=True, description='Username'),
    'email': fields.String(required=True, description='Email address')
})

@user_ns.route('/users')
class UserListResource(Resource):
    @user_ns.doc('list_users')
    @user_ns.marshal_list_with(user_model)
    @user_ns.response(500, 'Internal Server Error', error_model)
    def get(self):
        """Get list of all users"""
        try:
            users = User.query.all()
            return [user.to_dict() for user in users]
        except Exception as e:
            logger.error(f"Get users error: {str(e)}")
            user_ns.abort(500, f'Failed to get users: {str(e)}')
    
    @user_ns.doc('create_user')
    @user_ns.expect(create_user_model)
    @user_ns.marshal_with(user_model, code=201)
    @user_ns.response(400, 'Bad Request', error_model)
    @user_ns.response(500, 'Internal Server Error', error_model)
    def post(self):
        """Create a new user"""
        data = request.json
        
        if not data:
            user_ns.abort(400, 'Request body is required')
            
        username = data.get('username')
        email = data.get('email')
        
        if not username or not email:
            user_ns.abort(400, 'Username and email are required')
        
        try:
            user = User(username=username, email=email)
            db.session.add(user)
            db.session.commit()
            return user.to_dict(), 201
        except Exception as e:
            logger.error(f"Create user error: {str(e)}")
            db.session.rollback()
            user_ns.abort(500, f'Failed to create user: {str(e)}')


@user_ns.route('/users/<int:user_id>')
class UserResource(Resource):
    @user_ns.doc('get_user')
    @user_ns.marshal_with(user_model)
    @user_ns.response(404, 'User not found', error_model)
    @user_ns.response(500, 'Internal Server Error', error_model)
    def get(self, user_id):
        """Get a specific user by ID"""
        try:
            user = User.query.get(user_id)
            if not user:
                user_ns.abort(404, f'User with ID {user_id} not found')
            return user.to_dict()
        except Exception as e:
            logger.error(f"Get user error: {str(e)}")
            user_ns.abort(500, f'Failed to get user: {str(e)}')
