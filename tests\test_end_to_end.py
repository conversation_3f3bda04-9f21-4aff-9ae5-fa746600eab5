"""
End-to-End Test Runner

This script runs end-to-end tests for the AI Discogs application.
"""

import os
import sys
import unittest
import logging
import json
import time
from unittest.mock import patch, MagicMock

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# Import application services
from src.services.discogs_service import DiscogsService
from src.services.nlp_service import NLPService
from src.services.auth_service import AuthenticationService, UserProfileService
from src.services.database_service import DatabaseService
from src.services.settings_service import SettingsService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Download NLTK resources
import nltk
nltk.download('punkt')
nltk.download('stopwords')

class EndToEndTest(unittest.TestCase):
    """End-to-end test for the AI Discogs application."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        logger.info("Setting up end-to-end test environment")
        
        # Initialize services
        cls.db_service = DatabaseService(db_url="sqlite:///:memory:")
        cls.db_service.init_db()
        cls.settings_service = SettingsService(db_service=cls.db_service)
        cls.auth_service = AuthenticationService(
            consumer_key="test_key",
            consumer_secret="test_secret"
        )
        cls.discogs_service = DiscogsService(token="test_token")
        cls.nlp_service = NLPService()
        cls.profile_service = UserProfileService()
        
        logger.info("Test environment setup complete")
    
    def test_01_user_creation_and_profile(self):
        """Test user creation and profile management."""
        logger.info("Testing user creation and profile management")
        
        # Create a test user
        user = self.db_service.create_user("testuser", "<EMAIL>")
        user_id = user['id']  # Access dictionary key
        
        # Create a profile for the user
        profile_data = {
            'display_name': 'Test User',
            'bio': 'Test bio',
            'location': 'Test location'
        }
        profile = self.db_service.create_user_profile(user_id, profile_data)
        self.assertIsNotNone(profile)
        self.assertEqual(profile['display_name'], 'Test User')
        
        # Update the profile
        update_result = self.db_service.update_user_profile(user_id, {'bio': 'Updated bio'})
        self.assertTrue(update_result)
        
        # Get the updated profile
        updated_profile = self.db_service.get_user_profile(user_id)
        self.assertEqual(updated_profile['bio'], 'Updated bio')
        
        logger.info("User creation and profile management test passed")
    
    @patch('discogs_client.Client')
    def test_02_discogs_api_integration(self, mock_client):
        """Test Discogs API integration."""
        logger.info("Testing Discogs API integration")
        
        # Mock search results
        mock_result = MagicMock()
        mock_result.id = 1
        mock_result.type = "artist"
        mock_result.title = "Test Artist"
        
        mock_results = MagicMock()
        mock_results.page = 1
        mock_results.pages = 1
        mock_results.per_page = 10
        mock_results.count = 1
        mock_results.__iter__.return_value = [mock_result]
        
        mock_client_instance = mock_client.return_value
        mock_client_instance.search.return_value = mock_results
        
        # Test search
        service = DiscogsService(token="test_token")
        results = service.search("test query", search_type="artist")
        
        mock_client_instance.search.assert_called_once_with("test query", page=1, per_page=10, type="artist")
        self.assertEqual(results['pagination']['items'], 1)
        self.assertEqual(len(results['results']), 1)
        self.assertEqual(results['results'][0]['id'], 1)
        self.assertEqual(results['results'][0]['type'], "artist")
        self.assertEqual(results['results'][0]['title'], "Test Artist")
        
        logger.info("Discogs API integration test passed")
    
    def test_03_nlp_processing(self):
        """Test NLP processing of user queries."""
        logger.info("Testing NLP processing")
        
        # Test artist query
        query = "find artist Radiohead"
        result = self.nlp_service.process_query(query)
        
        self.assertTrue(result.get('matched_pattern', False))
        self.assertEqual(result.get('entity_type'), 'artist')
        
        # Test album query
        query = "show me albums by Radiohead"
        result = self.nlp_service.process_query(query)
        
        self.assertTrue(result.get('matched_pattern', False))
        self.assertEqual(result.get('entity_type'), 'album')
        self.assertEqual(result.get('artist'), 'radiohead')
        
        # Test search parameter generation
        search_params = self.nlp_service.generate_search_params(result)
        self.assertEqual(search_params['type'], 'album')
        self.assertEqual(search_params['query'], 'radiohead')
        
        logger.info("NLP processing test passed")
    
    def test_04_chat_session_management(self):
        """Test chat session management."""
        logger.info("Testing chat session management")
        
        # Create a test user and get the ID
        user_dict = {
            'username': 'chatuser',
            'email': '<EMAIL>'
        }
        session = self.db_service.get_session()
        try:
            user = self.db_service.create_user(user_dict['username'], user_dict['email'])
            user_id = user['id']  # Access dictionary key
            session.commit()
        finally:
            self.db_service.close_session()
        
        # Create a chat session
        chat_session = self.db_service.create_chat_session(user_id, "Test Session")
        session_id = chat_session['id']  # Access dictionary key
        
        # Add messages to the session
        user_message = self.db_service.add_chat_message(session_id, "user", "Find albums by Radiohead")
        self.assertIsNotNone(user_message)
        self.assertEqual(user_message['role'], "user")
        self.assertEqual(user_message['content'], "Find albums by Radiohead")
        
        assistant_message = self.db_service.add_chat_message(session_id, "assistant", "Here are some albums by Radiohead...")
        self.assertIsNotNone(assistant_message)
        self.assertEqual(assistant_message['role'], "assistant")
        
        # Get messages from the session
        messages = self.db_service.get_chat_messages(session_id)
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0]['content'], "Find albums by Radiohead")
        self.assertEqual(messages[1]['content'], "Here are some albums by Radiohead...")
        
        logger.info("Chat session management test passed")
    
    def test_05_settings_management(self):
        """Test settings management."""
        logger.info("Testing settings management")
        
        # Create a test user and get the ID
        user_dict = {
            'username': 'settingsuser',
            'email': '<EMAIL>'
        }
        session = self.db_service.get_session()
        try:
            user = self.db_service.create_user(user_dict['username'], user_dict['email'])
            user_id = user['id']  # Access dictionary key
            session.commit()
        finally:
            self.db_service.close_session()
        
        # Get default settings
        settings = self.settings_service.get_user_settings(user_id)
        self.assertIsNotNone(settings)
        self.assertEqual(settings['theme'], 'light')  # Default theme
        
        # Update settings
        update_result = self.settings_service.update_user_settings(user_id, {
            'theme': 'dark',
            'items_per_page': 20
        })
        self.assertTrue(update_result)
        
        # Get updated settings
        updated_settings = self.settings_service.get_user_settings(user_id)
        self.assertEqual(updated_settings['theme'], 'dark')
        self.assertEqual(updated_settings['items_per_page'], 20)
        
        # Set API keys
        api_key_result = self.settings_service.set_api_keys("test_consumer_key", "test_consumer_secret", user_id)
        self.assertTrue(api_key_result)
        
        # Get API keys
        api_keys = self.settings_service.get_api_keys(user_id)
        self.assertEqual(api_keys['consumer_key'], "test_consumer_key")
        self.assertEqual(api_keys['consumer_secret'], "test_consumer_secret")
        
        logger.info("Settings management test passed")
    
    def test_06_end_to_end_flow(self):
        """Test the complete end-to-end flow."""
        logger.info("Testing end-to-end flow")
        
        # Create a test user and get the ID
        user_dict = {
            'username': 'flowuser',
            'email': '<EMAIL>'
        }
        session = self.db_service.get_session()
        try:
            user = self.db_service.create_user(user_dict['username'], user_dict['email'])
            user_id = user['id']  # Access dictionary key
            session.commit()
        finally:
            self.db_service.close_session()
        
        # Create a profile
        self.db_service.create_user_profile(user_id, {
            'display_name': 'Flow User',
            'bio': 'Test user for flow testing'
        })
        
        # Set up user settings
        self.settings_service.update_user_settings(user_id, {
            'theme': 'dark',
            'discogs_api_key': 'flow_test_key'
        })
        
        # Create a chat session
        session = self.db_service.create_chat_session(user_id, "Flow Test Session")
        session_id = session['id']  # Access dictionary key
        
        # Simulate a chat interaction
        with patch.object(self.nlp_service, 'process_query') as mock_process:
            mock_process.return_value = {
                'matched_pattern': True,
                'entity_type': 'album',
                'artist': 'radiohead',
                'query': 'show me albums by radiohead'
            }
            
            with patch.object(self.nlp_service, 'generate_search_params') as mock_params:
                mock_params.return_value = {
                    'type': 'album',
                    'query': 'radiohead'
                }
                
                with patch.object(self.discogs_service, 'search') as mock_search:
                    mock_search.return_value = {
                        'pagination': {'items': 3, 'page': 1, 'pages': 1, 'per_page': 10},
                        'results': [
                            {'id': 1, 'type': 'album', 'title': 'OK Computer'},
                            {'id': 2, 'type': 'album', 'title': 'Kid A'},
                            {'id': 3, 'type': 'album', 'title': 'In Rainbows'}
                        ]
                    }
                    
                    with patch.object(self.nlp_service, 'format_response') as mock_format:
                        mock_format.return_value = "I found 3 albums by Radiohead: OK Computer, Kid A, and In Rainbows."
                        
                        # Process a user message
                        user_message = "Show me albums by Radiohead"
                        
                        # Add user message to chat
                        self.db_service.add_chat_message(session_id, "user", user_message)
                        
                        # Process the query
                        processed_query = self.nlp_service.process_query(user_message)
                        search_params = self.nlp_service.generate_search_params(processed_query)
                        search_results = self.discogs_service.search(
                            search_params.get('query'),
                            search_type=search_params.get('type')
                        )
                        response = self.nlp_service.format_response(search_results, processed_query)
                        
                        # Add assistant message to chat
                        self.db_service.add_chat_message(session_id, "assistant", response)
                        
                        # Verify the flow
                        mock_process.assert_called_once_with(user_message)
                        mock_params.assert_called_once_with(processed_query)
                        mock_search.assert_called_once_with('radiohead', search_type='album')
                        mock_format.assert_called_once()
                        
                        # Check chat history
                        messages = self.db_service.get_chat_messages(session_id)
                        self.assertEqual(len(messages), 2)
                        self.assertEqual(messages[0]['content'], user_message)
                        self.assertEqual(messages[1]['content'], response)
        
        logger.info("End-to-end flow test passed")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up after tests."""
        logger.info("Cleaning up test environment")
        # No cleanup needed for in-memory database
        logger.info("Test environment cleanup complete")


if __name__ == '__main__':
    logger.info("Starting end-to-end tests")
    unittest.main(verbosity=2)
