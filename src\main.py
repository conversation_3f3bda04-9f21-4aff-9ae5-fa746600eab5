"""
Main Application Entry Point for TuneScout

This module serves as the entry point for the TuneScout application,
initializing all necessary services and routes.
"""

import sys
import os
import logging
import threading
from dotenv import load_dotenv
from flask import Flask, render_template, jsonify, redirect
from flask_cors import CORS

# Load environment variables from .env file
load_dotenv()

# Download required NLTK data before importing services
import nltk
try:
    nltk.data.find('tokenizers/punkt_tab')
    nltk.data.find('corpora/stopwords')
except LookupError:
    logging.info("Downloading required NLTK data...")
    nltk.download('punkt_tab', quiet=True)
    nltk.download('stopwords', quiet=True)
    logging.info("NLTK data download completed")

from src.routes.user import user_bp
from src.routes.health import health_bp
from src.routes.search import search_bp

# Import API documentation components
from src.api import api_bp, api
from src.api.search_api import search_ns
from src.api.health_api import health_ns
from src.api.user_api import user_ns
from src.services.discogs_service import DiscogsService
from src.services.nlp_service import NLPService
from src.services.auth_service import AuthenticationService as AuthService
from src.services.database_service import DatabaseService
from src.services.settings_service import SettingsService
from src.frontend.chat_interface import create_chat_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Initialize Flask app
app = Flask(__name__, template_folder='templates', static_folder='static')
CORS(app)

# Ensure data directory exists
os.makedirs('data', exist_ok=True)

# Configure SQLAlchemy
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///data/tunescout.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize SQLAlchemy with app
from src.models.user import db
db.init_app(app)

# Create tables
with app.app_context():
    db.create_all()

# Register API namespaces for Swagger documentation
api.add_namespace(search_ns)
api.add_namespace(health_ns)
api.add_namespace(user_ns)

# Register blueprints
app.register_blueprint(user_bp)
app.register_blueprint(health_bp)
app.register_blueprint(search_bp, url_prefix='/api')
app.register_blueprint(api_bp)  # This will make Swagger UI available at /api/docs/

# Global variables for services (initialized later)
db_service = None
settings_service = None
discogs_service = None
nlp_service = None
auth_service = None
chat_interface = None

def initialize_services():
    """Initialize all services - called when app starts"""
    global db_service, settings_service, discogs_service, nlp_service, auth_service, chat_interface

    if db_service is None:  # Only initialize once
        db_service = DatabaseService()
        settings_service = SettingsService(db_service)
        discogs_token = settings_service.get_api_token() or os.environ.get('DISCOGS_TOKEN', '')
        discogs_service = DiscogsService(token=discogs_token, user_agent='TuneScout/1.0')
        nlp_service = NLPService(settings_service=settings_service)
        auth_service = AuthService(db_service)

        # Create Gradio interface
        chat_interface = create_chat_app(
            discogs_service=discogs_service,
            nlp_service=nlp_service,
            auth_service=auth_service,
            settings_service=settings_service
        )

def start_gradio():
    """Start Gradio interface"""
    global chat_interface

    # Make sure services are initialized
    initialize_services()

    if chat_interface is None:
        logging.error("Chat interface not initialized")
        return

    # Get Gradio port from environment variable, fallback to 7860
    gradio_port = int(os.environ.get('GRADIO_SERVER_PORT', 7860))
    logging.info(f"Starting Gradio interface on port {gradio_port}")

    try:
        chat_interface.launch(
            server_port=gradio_port,
            share=False,
            server_name="0.0.0.0",
            inbrowser=False,
            quiet=True
        )
    except OSError as e:
        logging.error(f"Failed to start Gradio on port {gradio_port}: {e}")
        # Try alternative ports if the configured port fails
        for alt_port in [7861, 7862, 7863, 8080, 8081]:
            try:
                logging.info(f"Trying alternative port {alt_port}")
                chat_interface.launch(
                    server_port=alt_port,
                    share=False,
                    server_name="0.0.0.0",
                    inbrowser=False,
                    quiet=True
                )
                logging.info(f"Successfully started Gradio on port {alt_port}")
                break
            except OSError:
                continue
        else:
            logging.error("Failed to start Gradio on any available port")
            raise

@app.route('/')
def index():
    """Render the main application page."""
    return render_template('index.html')

@app.route('/docs')
def docs_redirect():
    """Redirect to Swagger UI documentation."""
    return redirect('/api/docs/')

@app.route('/api/health')
def health():
    """API health check endpoint."""
    return jsonify({"status": "healthy", "app": "TuneScout"})

if __name__ == '__main__':
    # Initialize services when running as main
    initialize_services()

    # Start Gradio in a separate thread
    gradio_thread = threading.Thread(target=start_gradio, daemon=True)
    gradio_thread.start()

    # Run Flask app
    app.run(host="0.0.0.0", port=8000, debug=True)
