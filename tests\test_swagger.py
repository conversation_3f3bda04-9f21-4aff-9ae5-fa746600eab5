#!/usr/bin/env python3
"""Test Swagger UI setup"""
import os
import sys
import requests

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_swagger():
    """Test if Swagger UI is accessible"""
    try:
        # Get Flask port from environment or use default
        flask_port = int(os.environ.get('FLASK_PORT', 8000))
        
        # Test main health endpoint first
        health_url = f"http://127.0.0.1:{flask_port}/health"
        print(f"Testing health endpoint: {health_url}")
        health = requests.get(health_url, timeout=10)
        print(f"Health: {health.status_code} - {health.json()}")
        
        # Test API health endpoint
        api_health_url = f"http://127.0.0.1:{flask_port}/api/health"
        print(f"Testing API health endpoint: {api_health_url}")
        api_health = requests.get(api_health_url, timeout=10)
        print(f"API Health: {api_health.status_code} - {api_health.json()}")
        
        # Test Swagger UI redirect
        docs_redirect_url = f"http://127.0.0.1:{flask_port}/docs"
        print(f"Testing docs redirect: {docs_redirect_url}")
        docs_response = requests.get(docs_redirect_url, timeout=10, allow_redirects=False)
        print(f"Docs redirect: {docs_response.status_code} - Location: {docs_response.headers.get('Location', 'No redirect')}")
        
        # Test Swagger UI directly
        swagger_url = f"http://127.0.0.1:{flask_port}/api/docs/"
        print(f"Testing Swagger UI: {swagger_url}")
        swagger_response = requests.get(swagger_url, timeout=10)
        print(f"Swagger UI: {swagger_response.status_code}")
        
        if swagger_response.status_code == 200:
            print("✅ PASS: Swagger UI is accessible")
            # Check if it contains expected Swagger content
            if 'swagger' in swagger_response.text.lower() or 'openapi' in swagger_response.text.lower():
                print("✅ PASS: Swagger UI contains expected content")
            else:
                print("⚠️  WARNING: Swagger UI accessible but content may be incomplete")
        else:
            print("❌ FAIL: Swagger UI not accessible")
            
        # Test API endpoints
        search_url = f"http://127.0.0.1:{flask_port}/api/search?q=test"
        print(f"Testing search API: {search_url}")
        search_response = requests.get(search_url, timeout=10)
        print(f"Search API: {search_response.status_code}")
        
        if search_response.status_code in [200, 400]:  # 400 is expected if Discogs token is missing
            print("✅ PASS: Search API endpoint is responding")
        else:
            print("❌ FAIL: Search API endpoint not responding correctly")
            
    except requests.exceptions.ConnectionError:
        print("❌ FAIL: Cannot connect to Flask application. Make sure it's running on the expected port.")
    except Exception as e:
        print(f"❌ FAIL: Test failed: {e}")

if __name__ == "__main__":
    print("Testing Swagger UI setup...")
    print("Make sure the Flask application is running first!")
    print("=" * 50)
    test_swagger()
