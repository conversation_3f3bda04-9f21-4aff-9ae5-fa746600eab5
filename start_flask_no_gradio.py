#!/usr/bin/env python3
"""Start Flask app without Gradio for testing"""
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Start Flask app without Gradio"""
    print("Starting Flask app without Gradio...")
    
    # Set testing mode to avoid database issues
    os.environ['TESTING'] = '1'
    
    # Import the app
    from src.main import app, initialize_services
    
    # Initialize services
    print("Initializing services...")
    initialize_services()
    print("Services initialized!")
    
    # Test basic functionality
    print("Testing basic routes...")
    with app.test_client() as client:
        # Test health endpoint
        response = client.get('/health')
        print(f"✅ Health endpoint: {response.status_code}")
        
        # Test docs redirect
        response = client.get('/docs')
        print(f"✅ Docs redirect: {response.status_code} -> {response.location if response.status_code == 302 else 'No redirect'}")
        
        # Test API health
        response = client.get('/api/health')
        print(f"✅ API health: {response.status_code}")
        
        # Test Swagger UI
        response = client.get('/api/docs/')
        print(f"✅ Swagger UI: {response.status_code}")
    
    print("\nAll tests passed! Starting Flask server...")
    print("Swagger UI will be available at: http://localhost:5000/docs")
    print("=" * 60)
    
    # Run Flask app without Gradio
    app.run(host="0.0.0.0", port=5000, debug=True, use_reloader=False)

if __name__ == "__main__":
    main()
