"""
NLP Service for Discogs

This module provides NLP capabilities for processing natural language queries
related to music and Discogs data.
"""

import logging
import os
import re
from typing import Dict, List, Any, Optional, Tuple
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate
from langchain_community.llms import OpenAI
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# Download necessary NLTK data
try:
    nltk.data.find('tokenizers/punkt_tab')
except LookupError:
    nltk.download('punkt_tab')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

logger = logging.getLogger(__name__)

class NLPService:
    """Service for processing natural language queries related to music and Discogs."""
    
    # Entity types that can be extracted from queries
    ENTITY_TYPES = {
        'ARTIST': r'(?:artist|band|musician|composer|dj|producer)',
        'ALBUM': r'(?:album|record|release|lp|ep|compilation)',
        'TRACK': r'(?:track|song|piece|composition)',
        'LABEL': r'(?:label|publisher|imprint)',
        'GENRE': r'(?:genre|style|type of music)',
        'YEAR': r'(?:year|in \d{4}|from \d{4}|during \d{4}s)',
    }
    
    # Common search patterns
    SEARCH_PATTERNS = [
        # Find by name
        r'(?:find|get|search for|look for|show me|display|list)\s+(?:the\s+)?(?P<entity_type>artist|album|track|song|label)s?\s+(?:called|named|titled)?\s+["\']?(?P<name>[^"\']+)["\']?',
        
        # Find by artist
        r'(?:find|get|search for|look for|show me|display|list)\s+(?:the\s+)?(?P<entity_type>album|track|song|release)s?\s+by\s+(?:the\s+)?(?:artist|band)?\s*["\']?(?P<artist>[^"\']+)["\']?',
        
        # Find by genre
        r'(?:find|get|search for|look for|show me|display|list)\s+(?P<entity_type>artist|album|track|song|label)s?\s+in\s+(?:the\s+)?(?:genre|style|category)?\s*["\']?(?P<genre>[^"\']+)["\']?',
        
        # Find by year
        r'(?:find|get|search for|look for|show me|display|list)\s+(?P<entity_type>artist|album|track|song|label)s?\s+from\s+(?:the\s+)?(?:year)?\s*(?P<year>\d{4})',
        
        # Find latest/newest
        r'(?:find|get|search for|look for|show me|display|list)\s+(?:the\s+)?(?:latest|newest|most recent)\s+(?P<entity_type>album|track|song|release)s?\s+(?:by\s+(?:the\s+)?(?:artist|band)?\s*["\']?(?P<artist>[^"\']+)["\']?)?',
        
        # Simple find artist pattern
        r'find\s+(?:the\s+)?artist\s+(?P<name>[^"\']+)',
    ]
    
    def __init__(self, settings_service: Optional[Any] = None):
        """Initialize the NLP service."""
        self.stop_words = set(stopwords.words('english'))
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        self.settings_service = settings_service
        self.llm = None
        self.llm_chain = None

        if self.settings_service:
            self._initialize_llm()
        
    def _initialize_llm(self):
        """Initialize the LLM based on settings."""
        if not self.settings_service:
            logger.warning("SettingsService not provided to NLPService. LLM will not be initialized.")
            return

        provider = self.settings_service.get_app_setting('ai', 'llm_provider', 'openai')
        api_key = self.settings_service.get_llm_api_key(provider)
        model_name = self.settings_service.get_app_setting('ai', 'model', 'default')
        temperature = self.settings_service.get_app_setting('ai', 'temperature', 0.7)
        max_tokens = self.settings_service.get_app_setting('ai', 'max_tokens', 1000)

        if not api_key:
            logger.warning(f"API key for {provider} not found. LLM will not be initialized.")
            return

        try:
            if provider == 'openai':
                openai_model = model_name if model_name != 'default' else os.getenv('LLM_MODEL', 'gpt-4o-mini') 
                self.llm = ChatOpenAI(openai_api_key=api_key, model_name=openai_model, temperature=temperature, max_tokens=max_tokens)
                logger.info(f"Initialized OpenAI LLM with model: {openai_model}")
            elif provider == 'anthropic':
                anthropic_model = model_name if model_name != 'default' else 'claude-2' 
                self.llm = ChatAnthropic(anthropic_api_key=api_key, model_name=anthropic_model, temperature=temperature, max_tokens=max_tokens)
                logger.info(f"Initialized Anthropic LLM with model: {anthropic_model}")
            else:
                logger.warning(f"Unsupported LLM provider: {provider}. LLM not initialized.")
                return

            if self.llm:
                prompt_template_str = "You are a helpful assistant. {question}"
                prompt = PromptTemplate.from_template(prompt_template_str)
                self.llm_chain = prompt | self.llm
                logger.info("LLMChain (prompt | llm) initialized.")

        except Exception as e:
            logger.error(f"Error initializing LLM provider {provider} with model {model_name}: {str(e)}")
            self.llm = None
            self.llm_chain = None

    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Process a natural language query to extract search parameters.
        
        Args:
            query: Natural language query string
            
        Returns:
            Dictionary containing extracted search parameters
        """
        try:
            # Clean and normalize the query
            cleaned_query = self._clean_query(query)
            
            # Try to match against common patterns
            pattern_match = self._match_patterns(cleaned_query)
            if pattern_match:
                return pattern_match
            
            # Fall back to entity extraction
            return self._extract_entities(cleaned_query)
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {'query': query, 'error': str(e), 'matched_pattern': False}
    
    def _clean_query(self, query: str) -> str:
        """Clean and normalize a query string."""
        # Convert to lowercase
        query = query.lower()
        
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query).strip()
        
        # Remove punctuation that might interfere with pattern matching
        query = re.sub(r'[,.;:!?]', ' ', query)
        
        return query
    
    def _match_patterns(self, query: str) -> Optional[Dict[str, Any]]:
        """Try to match the query against common search patterns."""
        for pattern in self.SEARCH_PATTERNS:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                result = {'query': query, 'matched_pattern': True}
                result.update(match.groupdict())
                
                # Convert entity_type to standard form
                if 'entity_type' in result:
                    entity_type = result['entity_type'].lower()
                    if entity_type in ('song', 'piece', 'composition'):
                        result['entity_type'] = 'track'
                    elif entity_type in ('record', 'release', 'lp', 'ep', 'compilation'):
                        result['entity_type'] = 'album'
                elif 'name' in result:
                    # If we have a name but no entity_type, it's likely an artist search
                    result['entity_type'] = 'artist'
                
                return result
        
        return None
    
    def _extract_entities(self, query: str) -> Dict[str, Any]:
        """Extract entities and intent from a query when pattern matching fails."""
        result = {'query': query, 'matched_pattern': False}
        
        # Extract potential entity types
        for entity_type, pattern in self.ENTITY_TYPES.items():
            if re.search(pattern, query, re.IGNORECASE):
                result['entity_type'] = entity_type.lower()
                break
        
        # If no entity type was found, default to a general search
        if 'entity_type' not in result:
            result['entity_type'] = 'general'
        
        # Extract potential search terms by removing stop words
        tokens = word_tokenize(query)
        search_terms = [word for word in tokens if word.lower() not in self.stop_words and len(word) > 2]
        result['search_terms'] = search_terms
        
        return result
    
    def generate_search_params(self, processed_query: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate search parameters for the Discogs API based on processed query.
        
        Args:
            processed_query: Dictionary containing processed query information
            
        Returns:
            Dictionary containing search parameters for the Discogs API
        """
        search_params = {'query': processed_query.get('query', '')}
        
        # Handle pattern-matched queries
        if processed_query.get('matched_pattern', False):
            entity_type = processed_query.get('entity_type')
            
            if entity_type:
                search_params['search_type'] = entity_type
            
            # Add name parameter if available
            if 'name' in processed_query:
                search_params['query'] = processed_query['name']
            
            # Add artist parameter if available
            if 'artist' in processed_query:
                if entity_type == 'album' or entity_type == 'track':
                    search_params['artist'] = processed_query['artist']
                    search_params['query'] = f"{processed_query['artist']}"
            
            # Add genre parameter if available
            if 'genre' in processed_query:
                search_params['genre'] = processed_query['genre']
            
            # Add year parameter if available
            if 'year' in processed_query:
                search_params['year'] = processed_query['year']
                
        # Handle entity-extracted queries
        else:
            entity_type = processed_query.get('entity_type')
            
            if entity_type and entity_type != 'general':
                search_params['search_type'] = entity_type
            
            # Use extracted search terms
            search_terms = processed_query.get('search_terms', [])
            if search_terms:
                search_params['query'] = ' '.join(search_terms)
        
        return search_params
    
    def process_message(self, message: str, discogs_service: Any) -> str:
        """
        Process a complete chat message with Discogs API integration.
        
        Args:
            message: User's chat message
            discogs_service: Discogs service instance for API calls
            
        Returns:
            Formatted response string
        """
        try:
            # Process the user's query
            processed_query = self.process_query(message)
            
            # Generate search parameters for Discogs API
            search_params = self.generate_search_params(processed_query)
            
            # Perform search using Discogs service
            search_results = discogs_service.search(**search_params)
            
            # Format and return the response
            return self.format_response(search_results, processed_query)
            
        except Exception as e:
            logger.error(f"Error processing message '{message}': {str(e)}")
            return f"Sorry, I encountered an error while searching: {str(e)}"
    
    def format_response(self, search_results: Dict[str, Any], processed_query: Dict[str, Any]) -> str:
        """
        Format search results into a natural language response.
        
        Args:
            search_results: Dictionary containing search results from the Discogs API
            processed_query: Dictionary containing processed query information
            
        Returns:
            Formatted natural language response
        """
        try:
            # Extract relevant information
            query = processed_query.get('query', '')
            entity_type = processed_query.get('entity_type', 'item')
            results = search_results.get('results', [])
            pagination = search_results.get('pagination', {})
            total_items = pagination.get('items', 0)
            
            # Handle no results
            if not results or total_items == 0:
                return f"I couldn't find any {entity_type}s matching '{query}'. Would you like to try a different search?"
            
            # Format response based on entity type and results
            if entity_type == 'artist':
                response = f"I found {total_items} artists matching '{query}'.\n\n"
                for i, artist in enumerate(results[:5], 1):
                    title = artist.get('title', 'Unknown Artist')
                    
                    # Build rich artist card
                    response += f"""<div style="border: 1px solid #444; border-radius: 8px; padding: 12px; margin: 8px 0; background: #2a2a2a; overflow: hidden; display: flex; align-items: center;">"""
                    if artist.get('thumb'):
                        response += f"""<img src="{artist['thumb']}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%; margin-right: 12px; flex-shrink: 0;" alt="Artist Photo" />"""
                    
                    response += f"""<div style="flex: 1; min-width: 0;"><h4 style="margin: 0 0 4px 0; color: #fff; overflow: hidden; text-overflow: ellipsis;">🎤 {i}. {title}</h4>"""
                    if artist.get('resource_url'):
                        response += f"""<p style="margin: 2px 0; color: #888; font-size: 0.8em;">ID: {artist['id']}</p>"""
                    
                    response += "</div></div>\n"
                
            elif entity_type == 'album':
                response = f"I found {total_items} albums matching '{query}'.\n\n"
                for i, album in enumerate(results[:5], 1):
                    title = album.get('title', 'Unknown Album')
                    year = album.get('year', '')
                    genre = ', '.join(album.get('genre', [])[:2]) if album.get('genre') else ''
                    label = ', '.join(album.get('label', [])[:2]) if album.get('label') else ''
                    format_info = ', '.join(album.get('format', [])[:2]) if album.get('format') else ''
                    
                    # Build rich card
                    response += f"""<div style="border: 1px solid #444; border-radius: 8px; padding: 12px; margin: 8px 0; background: #2a2a2a; overflow: hidden; display: flex; align-items: flex-start;">"""
                    if album.get('thumb'):
                        response += f"""<img src="{album['thumb']}" style="width: 70px; height: 70px; object-fit: cover; border-radius: 4px; margin-right: 12px; flex-shrink: 0;" alt="Album Cover" />"""
                    
                    response += f"""<div style="flex: 1; min-width: 0;"><h4 style="margin: 0 0 4px 0; color: #fff; overflow: hidden; text-overflow: ellipsis;">{i}. {title}</h4>"""
                    if year:
                        response += f"""<p style="margin: 2px 0; color: #ccc; font-size: 0.9em;">📅 {year}</p>"""
                    if genre:
                        response += f"""<p style="margin: 2px 0; color: #ccc; font-size: 0.9em; overflow: hidden; text-overflow: ellipsis;">🎵 {genre}</p>"""
                    if label:
                        response += f"""<p style="margin: 2px 0; color: #ccc; font-size: 0.9em; overflow: hidden; text-overflow: ellipsis;">🏷️ {label}</p>"""
                    if format_info:
                        response += f"""<p style="margin: 2px 0; color: #ccc; font-size: 0.9em; overflow: hidden; text-overflow: ellipsis;">💿 {format_info}</p>"""
                    
                    response += "</div></div>\n"
                
            elif entity_type == 'track':
                response = f"I found {total_items} tracks matching '{query}'.\n\n"
                for i, track in enumerate(results[:5], 1):
                    response += f"{i}. {track.get('title', 'Unknown Track')}\n"
                
            elif entity_type == 'label':
                response = f"I found {total_items} labels matching '{query}'.\n\n"
                for i, label in enumerate(results[:5], 1):
                    response += f"{i}. {label.get('title', 'Unknown Label')}\n"
                
            else:
                response = f"I found {total_items} items matching '{query}'.\n\n"
                for i, item in enumerate(results[:5], 1):
                    response += f"{i}. {item.get('title', 'Unknown Item')} ({item.get('type', 'item')})\n"
            
            # Add pagination information if there are more results
            if total_items > 5:
                response += f"\nShowing 5 of {total_items} results. Would you like to see more?"
                
            return response
        except Exception as e:
            logger.error(f"Error formatting response: {str(e)}")
            return f"I found some results for '{query}', but encountered an error formatting the response."
