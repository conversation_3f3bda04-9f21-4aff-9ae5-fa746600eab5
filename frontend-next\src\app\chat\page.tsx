'use client'

import { useState } from 'react'
import { ChatInput } from '@/components/ChatInput'
import { ChatHistory } from '@/components/ChatHistory'
import { ResultCard } from '@/components/ResultCard'

interface Message {
  role: 'user' | 'assistant'
  content: string
  results?: any[]
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [resultCache, setResultCache] = useState<Record<number, any>>({})

  const handleSendMessage = async (content: string) => {
    setLoading(true)
    const userMessage: Message = { role: 'user', content }
    setMessages(prev => [...prev, userMessage])

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/api/search?q=${encodeURIComponent(content)}`)
      const data = await response.json()
      
      if (data.results) {
        data.results.forEach((result: any, idx: number) => {
          setResultCache(prev => ({ ...prev, [idx]: result }))
        })
        
        const assistantMessage: Message = {
          role: 'assistant',
          content: `Found ${data.results.length} results`,
          results: data.results
        }
        setMessages(prev => [...prev, assistantMessage])
      }
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: 'Search failed. Please try again.'
      }
      setMessages(prev => [...prev, errorMessage])
    }
    
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-chat">
        <h1 className="text-3xl font-bold mb-8 text-center">TuneScout Chat</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <ChatHistory messages={messages} resultCache={resultCache} />
          <ChatInput onSend={handleSendMessage} loading={loading} />
        </div>
      </div>
    </div>
  )
}
