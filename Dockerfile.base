FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install ML dependencies (PyTorch, CUDA, etc.)
RUN pip install --no-cache-dir \
    torch==2.7.0 \
    "sentence-transformers>=2.2.2" \
    "transformers>=4.41.0,<5.0.0" \
    numpy \
    scipy \
    scikit-learn \
    nltk

# Download NLTK data
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"

WORKDIR /app