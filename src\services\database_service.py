"""
Database Service for User Profiles and History

This module provides database operations for user profiles and chat history
using SQLAlchemy.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session, Session
from sqlalchemy.exc import SQLAlchemyError

from src.models.database_models import Base, User, UserProfile, UserSettings, ChatSession, ChatMessage, MusicEntity, UserInteraction

logger = logging.getLogger(__name__)

class DatabaseService:
    """Service for database operations."""
    
    def __init__(self, db_url: Optional[str] = None):
        """
        Initialize the database service.
        
        Args:
            db_url: SQLAlchemy database URL
        """
        if db_url is None:
            db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'discogs_app.db')
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            db_url = f"sqlite:///{db_path}"
        
        self.engine = create_engine(db_url)
        self.session_factory = sessionmaker(bind=self.engine)
        self.Session = scoped_session(self.session_factory)
        
    def init_db(self) -> None:
        """Initialize the database schema."""
        try:
            Base.metadata.create_all(self.engine)
            logger.info("Database schema created successfully")
        except SQLAlchemyError as e:
            logger.error(f"Error creating database schema: {str(e)}")
            raise
    
    def get_session(self) -> Session:
        """Get a database session."""
        return self.Session()
    
    def close_session(self) -> None:
        """Close the current database session."""
        self.Session.remove()
    
    # User operations
    
    def create_user(self, username: str, email: Optional[str] = None, 
                   oauth_token: Optional[str] = None, oauth_token_secret: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new user.
        
        Args:
            username: User's username
            email: User's email address
            oauth_token: OAuth token
            oauth_token_secret: OAuth token secret
            
        Returns:
            Dictionary containing user data
        """
        try:
            session = self.get_session()
            user = User(
                username=username,
                email=email,
                oauth_token=oauth_token,
                oauth_token_secret=oauth_token_secret
            )
            session.add(user)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
            
            session.commit()
            return user_data
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error creating user: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Get a user by username.
        
        Args:
            username: User's username
            
        Returns:
            Dictionary containing user data if found, None otherwise
        """
        try:
            session = self.get_session()
            user = session.query(User).filter(User.username == username).first()
            
            if not user:
                return None
                
            # Convert to dictionary
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'oauth_token': user.oauth_token,
                'oauth_token_secret': user.oauth_token_secret
            }
            
            return user_data
        except SQLAlchemyError as e:
            logger.error(f"Error getting user by username: {str(e)}")
            return None
        finally:
            self.close_session()
    
    def update_user(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update a user.
        
        Args:
            user_id: User ID
            updates: Dictionary of updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.get_session()
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return False
            
            for key, value in updates.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            
            session.commit()
            return True
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error updating user: {str(e)}")
            return False
        finally:
            self.close_session()
    
    # User profile operations
    
    def create_user_profile(self, user_id: int, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a user profile.
        
        Args:
            user_id: User ID
            profile_data: Profile data
            
        Returns:
            Dictionary containing profile data
        """
        try:
            session = self.get_session()
            profile = UserProfile(user_id=user_id, **profile_data)
            session.add(profile)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            profile_dict = {
                'id': profile.id,
                'user_id': profile.user_id,
                'display_name': profile.display_name,
                'bio': profile.bio,
                'location': profile.location,
                'avatar_url': profile.avatar_url,
                'discogs_id': profile.discogs_id,
                'discogs_username': profile.discogs_username
            }
            
            session.commit()
            return profile_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error creating user profile: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_user_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a user profile.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary containing profile data if found, None otherwise
        """
        try:
            session = self.get_session()
            profile = session.query(UserProfile).filter(UserProfile.user_id == user_id).first()
            
            if not profile:
                return None
                
            # Convert to dictionary
            profile_dict = {
                'id': profile.id,
                'user_id': profile.user_id,
                'display_name': profile.display_name,
                'bio': profile.bio,
                'location': profile.location,
                'avatar_url': profile.avatar_url,
                'discogs_id': profile.discogs_id,
                'discogs_username': profile.discogs_username,
                'discogs_url': profile.discogs_url,
                'discogs_collection_count': profile.discogs_collection_count,
                'discogs_wantlist_count': profile.discogs_wantlist_count,
                'music_preferences': profile.music_preferences,
                'favorite_genres': profile.favorite_genres,
                'favorite_artists': profile.favorite_artists
            }
            
            return profile_dict
        except SQLAlchemyError as e:
            logger.error(f"Error getting user profile: {str(e)}")
            return None
        finally:
            self.close_session()
    
    def update_user_profile(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update a user profile.
        
        Args:
            user_id: User ID
            updates: Dictionary of updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.get_session()
            profile = session.query(UserProfile).filter(UserProfile.user_id == user_id).first()
            
            if not profile:
                # Create profile if it doesn't exist
                profile = UserProfile(user_id=user_id)
                session.add(profile)
            
            for key, value in updates.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            
            session.commit()
            return True
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error updating user profile: {str(e)}")
            return False
        finally:
            self.close_session()
    
    # User settings operations
    
    def create_user_settings(self, user_id: int, settings_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create user settings.
        
        Args:
            user_id: User ID
            settings_data: Settings data
            
        Returns:
            Dictionary containing settings data
        """
        try:
            session = self.get_session()
            settings = UserSettings(user_id=user_id, **settings_data)
            session.add(settings)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            settings_dict = {
                'id': settings.id,
                'user_id': settings.user_id,
                'theme': settings.theme,
                'items_per_page': settings.items_per_page,
                'email_notifications': settings.email_notifications,
                'share_collection': settings.share_collection,
                'share_wantlist': settings.share_wantlist,
                'discogs_api_key': settings.discogs_api_key,
                'discogs_api_secret': settings.discogs_api_secret
            }
            
            session.commit()
            return settings_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error creating user settings: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_user_settings(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user settings.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary containing settings data if found, None otherwise
        """
        try:
            session = self.get_session()
            settings = session.query(UserSettings).filter(UserSettings.user_id == user_id).first()
            
            if not settings:
                return None
                
            # Convert to dictionary
            settings_dict = {
                'id': settings.id,
                'user_id': settings.user_id,
                'theme': settings.theme,
                'items_per_page': settings.items_per_page,
                'email_notifications': settings.email_notifications,
                'share_collection': settings.share_collection,
                'share_wantlist': settings.share_wantlist,
                'discogs_api_key': settings.discogs_api_key,
                'discogs_api_secret': settings.discogs_api_secret
            }
            
            return settings_dict
        except SQLAlchemyError as e:
            logger.error(f"Error getting user settings: {str(e)}")
            return None
        finally:
            self.close_session()
    
    def update_user_settings(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update user settings.
        
        Args:
            user_id: User ID
            updates: Dictionary of updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.get_session()
            settings = session.query(UserSettings).filter(UserSettings.user_id == user_id).first()
            
            if not settings:
                # Create settings if they don't exist
                settings = UserSettings(user_id=user_id)
                session.add(settings)
            
            for key, value in updates.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
            
            session.commit()
            return True
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error updating user settings: {str(e)}")
            return False
        finally:
            self.close_session()
    
    # Chat session operations
    
    def create_chat_session(self, user_id: int, title: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new chat session.
        
        Args:
            user_id: User ID
            title: Chat session title
            
        Returns:
            Dictionary containing chat session data
        """
        try:
            session = self.get_session()
            chat_session = ChatSession(user_id=user_id, title=title)
            session.add(chat_session)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            session_dict = {
                'id': chat_session.id,
                'user_id': chat_session.user_id,
                'title': chat_session.title,
                'created_at': chat_session.created_at.isoformat() if chat_session.created_at else None,
                'updated_at': chat_session.updated_at.isoformat() if chat_session.updated_at else None
            }
            
            session.commit()
            return session_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error creating chat session: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_chat_session(self, session_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a chat session by ID.
        
        Args:
            session_id: Chat session ID
            
        Returns:
            Dictionary containing chat session data if found, None otherwise
        """
        try:
            session = self.get_session()
            chat_session = session.query(ChatSession).filter(ChatSession.id == session_id).first()
            
            if not chat_session:
                return None
                
            # Convert to dictionary
            session_dict = {
                'id': chat_session.id,
                'user_id': chat_session.user_id,
                'title': chat_session.title,
                'created_at': chat_session.created_at.isoformat() if chat_session.created_at else None,
                'updated_at': chat_session.updated_at.isoformat() if chat_session.updated_at else None
            }
            
            return session_dict
        except SQLAlchemyError as e:
            logger.error(f"Error getting chat session: {str(e)}")
            return None
        finally:
            self.close_session()
    
    def get_user_chat_sessions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get all chat sessions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of dictionaries containing chat session data
        """
        try:
            session = self.get_session()
            chat_sessions = session.query(ChatSession).filter(ChatSession.user_id == user_id).all()
            
            # Convert to list of dictionaries
            session_dicts = []
            for chat_session in chat_sessions:
                session_dicts.append({
                    'id': chat_session.id,
                    'user_id': chat_session.user_id,
                    'title': chat_session.title,
                    'created_at': chat_session.created_at.isoformat() if chat_session.created_at else None,
                    'updated_at': chat_session.updated_at.isoformat() if chat_session.updated_at else None
                })
            
            return session_dicts
        except SQLAlchemyError as e:
            logger.error(f"Error getting user chat sessions: {str(e)}")
            return []
        finally:
            self.close_session()
    
    def add_chat_message(self, session_id: int, role: str, content: str, 
                        context: Optional[Dict[str, Any]] = None, 
                        references: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Add a message to a chat session.
        
        Args:
            session_id: Chat session ID
            role: Message role ('user' or 'assistant')
            content: Message content
            context: Message context
            references: Message references
            
        Returns:
            Dictionary containing chat message data
        """
        try:
            session = self.get_session()
            message = ChatMessage(
                session_id=session_id,
                role=role,
                content=content,
                context=context,
                references=references
            )
            session.add(message)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            message_dict = {
                'id': message.id,
                'session_id': message.session_id,
                'role': message.role,
                'content': message.content,
                'timestamp': message.timestamp.isoformat() if message.timestamp else None,
                'context': message.context,
                'references': message.references
            }
            
            session.commit()
            return message_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error adding chat message: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_chat_messages(self, session_id: int) -> List[Dict[str, Any]]:
        """
        Get all messages in a chat session.
        
        Args:
            session_id: Chat session ID
            
        Returns:
            List of dictionaries containing chat message data
        """
        try:
            session = self.get_session()
            messages = session.query(ChatMessage).filter(ChatMessage.session_id == session_id).order_by(ChatMessage.timestamp).all()
            
            # Convert to list of dictionaries
            message_dicts = []
            for message in messages:
                message_dicts.append({
                    'id': message.id,
                    'session_id': message.session_id,
                    'role': message.role,
                    'content': message.content,
                    'timestamp': message.timestamp.isoformat() if message.timestamp else None,
                    'context': message.context,
                    'references': message.references
                })
            
            return message_dicts
        except SQLAlchemyError as e:
            logger.error(f"Error getting chat messages: {str(e)}")
            return []
        finally:
            self.close_session()
    
    # Music entity operations
    
    def create_or_update_music_entity(self, discogs_id: int, entity_type: str, name: str, 
                                     year: Optional[int] = None, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create or update a music entity.
        
        Args:
            discogs_id: Discogs ID
            entity_type: Entity type ('artist', 'release', 'master', 'label')
            name: Entity name
            year: Entity year
            data: Additional entity data
            
        Returns:
            Dictionary containing music entity data
        """
        try:
            session = self.get_session()
            entity = session.query(MusicEntity).filter(
                MusicEntity.discogs_id == discogs_id,
                MusicEntity.type == entity_type
            ).first()
            
            if not entity:
                entity = MusicEntity(
                    discogs_id=discogs_id,
                    type=entity_type,
                    name=name,
                    year=year,
                    data=data
                )
                session.add(entity)
            else:
                entity.name = name
                entity.year = year
                entity.data = data
            
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            entity_dict = {
                'id': entity.id,
                'discogs_id': entity.discogs_id,
                'type': entity.type,
                'name': entity.name,
                'year': entity.year,
                'data': entity.data,
                'created_at': entity.created_at.isoformat() if entity.created_at else None,
                'updated_at': entity.updated_at.isoformat() if entity.updated_at else None
            }
            
            session.commit()
            return entity_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error creating or updating music entity: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_music_entity(self, discogs_id: int, entity_type: str) -> Optional[Dict[str, Any]]:
        """
        Get a music entity by Discogs ID and type.
        
        Args:
            discogs_id: Discogs ID
            entity_type: Entity type ('artist', 'release', 'master', 'label')
            
        Returns:
            Dictionary containing music entity data if found, None otherwise
        """
        try:
            session = self.get_session()
            entity = session.query(MusicEntity).filter(
                MusicEntity.discogs_id == discogs_id,
                MusicEntity.type == entity_type
            ).first()
            
            if not entity:
                return None
                
            # Convert to dictionary
            entity_dict = {
                'id': entity.id,
                'discogs_id': entity.discogs_id,
                'type': entity.type,
                'name': entity.name,
                'year': entity.year,
                'data': entity.data,
                'created_at': entity.created_at.isoformat() if entity.created_at else None,
                'updated_at': entity.updated_at.isoformat() if entity.updated_at else None
            }
            
            return entity_dict
        except SQLAlchemyError as e:
            logger.error(f"Error getting music entity: {str(e)}")
            return None
        finally:
            self.close_session()
    
    # User interaction operations
    
    def record_user_interaction(self, user_id: int, entity_id: int, 
                              interaction_type: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Record a user interaction with a music entity.
        
        Args:
            user_id: User ID
            entity_id: Music entity ID
            interaction_type: Interaction type ('view', 'search', 'like', etc.)
            data: Additional interaction data
            
        Returns:
            Dictionary containing user interaction data
        """
        try:
            session = self.get_session()
            interaction = UserInteraction(
                user_id=user_id,
                entity_id=entity_id,
                interaction_type=interaction_type,
                data=data
            )
            session.add(interaction)
            session.flush()  # Flush to get the ID without committing
            
            # Extract data before committing and closing session
            interaction_dict = {
                'id': interaction.id,
                'user_id': interaction.user_id,
                'entity_id': interaction.entity_id,
                'interaction_type': interaction.interaction_type,
                'timestamp': interaction.timestamp.isoformat() if interaction.timestamp else None,
                'data': interaction.data
            }
            
            session.commit()
            return interaction_dict
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Error recording user interaction: {str(e)}")
            raise
        finally:
            self.close_session()
    
    def get_user_interactions(self, user_id: int, interaction_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get user interactions.
        
        Args:
            user_id: User ID
            interaction_type: Optional interaction type filter
            
        Returns:
            List of dictionaries containing user interaction data
        """
        try:
            session = self.get_session()
            query = session.query(UserInteraction).filter(UserInteraction.user_id == user_id)
            
            if interaction_type:
                query = query.filter(UserInteraction.interaction_type == interaction_type)
            
            interactions = query.order_by(UserInteraction.timestamp.desc()).all()
            
            # Convert to list of dictionaries
            interaction_dicts = []
            for interaction in interactions:
                interaction_dicts.append({
                    'id': interaction.id,
                    'user_id': interaction.user_id,
                    'entity_id': interaction.entity_id,
                    'interaction_type': interaction.interaction_type,
                    'timestamp': interaction.timestamp.isoformat() if interaction.timestamp else None,
                    'data': interaction.data
                })
            
            return interaction_dicts
        except SQLAlchemyError as e:
            logger.error(f"Error getting user interactions: {str(e)}")
            return []
        finally:
            self.close_session()
