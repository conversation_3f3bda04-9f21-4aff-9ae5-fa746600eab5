#!/usr/bin/env python3
"""Test chat interface and Gradio setup"""
import os
import sys
import requests

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_chat():
    """Test chat interface accessibility"""
    try:
        # Test Flask health first (local development uses port 1975)
        health = requests.get("http://127.0.0.1:1975/health", timeout=5)
        print(f"Flask Health: {health.status_code} - {health.json()}")

        # Get Gradio port from environment variable, fallback to 7860
        gradio_port = int(os.environ.get('GRADIO_SERVER_PORT', 7860))

        # Test chat at configured Gradio port
        chat_response = requests.get(f"http://127.0.0.1:{gradio_port}", timeout=5)
        print(f"Chat interface on port {gradio_port}: {chat_response.status_code}")

        if chat_response.status_code == 200:
            print("✅ PASS: Chat interface accessible")
        else:
            print("❌ FAIL: Chat interface failed")
            
        # Test API endpoints as well
        api_health = requests.get("http://127.0.0.1:1975/api/health", timeout=5)
        print(f"API Health: {api_health.status_code} - {api_health.json()}")

    except Exception as e:
        print(f"❌ FAIL: Test failed: {e}")

def test_full_stack():
    """Test both Flask API and Gradio chat interface"""
    print("Testing TuneScout full stack...")
    print("=" * 50)
    
    print("\n1. Testing Flask API...")
    test_chat()
    
    print("\n2. Testing Swagger UI...")
    try:
        swagger_response = requests.get("http://127.0.0.1:1975/docs", timeout=5, allow_redirects=False)
        print(f"Swagger redirect: {swagger_response.status_code}")

        swagger_ui = requests.get("http://127.0.0.1:1975/api/docs/", timeout=5)
        print(f"Swagger UI: {swagger_ui.status_code}")
        
        if swagger_ui.status_code == 200:
            print("✅ PASS: Swagger UI accessible")
        else:
            print("❌ FAIL: Swagger UI not accessible")
    except Exception as e:
        print(f"❌ FAIL: Swagger test failed: {e}")
    
    print("\n" + "=" * 50)
    print("Full stack test completed!")

if __name__ == "__main__":
    test_full_stack()
