#!/usr/bin/env python3
"""
Test application startup with Swagger
"""
import os
import sys
import logging
import unittest

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestAppStartup(unittest.TestCase):
    """Test application startup and basic functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Set up basic logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def test_import_main_app(self):
        """Test that the main app can be imported without errors"""
        try:
            from src.main import app
            self.assertIsNotNone(app)
            print("✅ PASS: Main app imports successfully")
        except ImportError as e:
            self.fail(f"Failed to import main app: {e}")
    
    def test_api_blueprint_registration(self):
        """Test that API blueprints are properly registered"""
        try:
            from src.main import app
            
            # Check if blueprints are registered
            blueprint_names = [bp.name for bp in app.blueprints.values()]
            
            expected_blueprints = ['user', 'health', 'search', 'api']
            for bp_name in expected_blueprints:
                self.assertIn(bp_name, blueprint_names, f"Blueprint '{bp_name}' not registered")
            
            print("✅ PASS: All expected blueprints are registered")
        except Exception as e:
            self.fail(f"Failed to check blueprint registration: {e}")
    
    def test_swagger_routes_exist(self):
        """Test that Swagger routes are properly configured"""
        try:
            from src.main import app
            
            with app.test_client() as client:
                # Test docs redirect
                response = client.get('/docs')
                self.assertEqual(response.status_code, 302, "Docs redirect should return 302")
                self.assertIn('/api/docs/', response.location, "Should redirect to /api/docs/")
                
                # Test API health endpoint
                response = client.get('/api/health')
                self.assertEqual(response.status_code, 200, "API health endpoint should return 200")
                
                print("✅ PASS: Swagger routes are properly configured")
        except Exception as e:
            self.fail(f"Failed to test Swagger routes: {e}")
    
    def test_database_initialization(self):
        """Test that database is properly initialized"""
        try:
            from src.main import app
            from src.models.user import db
            
            with app.app_context():
                # Check if tables exist
                inspector = db.inspect(db.engine)
                tables = inspector.get_table_names()
                self.assertIn('user', tables, "User table should exist")
                
                print("✅ PASS: Database is properly initialized")
        except Exception as e:
            self.fail(f"Failed to test database initialization: {e}")

def run_startup_test():
    """Run startup tests"""
    print("Testing TuneScout application startup...")
    print("=" * 60)
    
    # Run the tests
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "=" * 60)
    print("Startup tests completed!")
    print("\nTo start the application with Swagger UI:")
    print("  python -m src.main")
    print("\nSwagger UI will be available at:")
    print("  - http://localhost:8000/docs (redirect)")
    print("  - http://localhost:8000/api/docs/ (direct)")

if __name__ == "__main__":
    run_startup_test()
