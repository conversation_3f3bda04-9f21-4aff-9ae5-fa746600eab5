#!/usr/bin/env python3
"""
Start Flask app only (without Gradio) for testing Swagger UI
"""
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Start Flask app only"""
    print("Starting Flask app only (no Gradio)...")
    print("Swagger UI will be available at:")
    print("  - http://localhost:5000/docs (redirect)")
    print("  - http://localhost:5000/api/docs/ (direct)")
    print("=" * 60)
    
    # Import the app
    from src.main import app, initialize_services
    
    # Initialize services
    initialize_services()
    
    # Run Flask app only on port 5000
    app.run(host="0.0.0.0", port=5000, debug=True, use_reloader=False)

if __name__ == "__main__":
    main()
