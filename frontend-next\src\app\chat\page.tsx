'use client'

import { useState } from 'react'
import { ChatInput } from '@/components/ChatInput'
import { ChatHistory } from '@/components/ChatHistory'
import { ResultCard } from '@/components/ResultCard'
import { api, SearchResult } from '@/lib/api/client'

interface Message {
  role: 'user' | 'assistant'
  content: string
  results?: SearchResult[]
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [resultCache, setResultCache] = useState<Record<number, SearchResult>>({})

  const handleSendMessage = async (content: string) => {
    setLoading(true)
    const userMessage: Message = { role: 'user', content }
    setMessages(prev => [...prev, userMessage])

    try {
      const data = await api.search({ q: content })
      
      if (data.results) {
        data.results.forEach((result: SearchResult, idx: number) => {
          setResultCache(prev => ({ ...prev, [idx]: result }))
        })
        
        const assistantMessage: Message = {
          role: 'assistant',
          content: `Found ${data.results.length} results`,
          results: data.results
        }
        setMessages(prev => [...prev, assistantMessage])
      }
    } catch (error) {
      const errorMessage: Message = {
        role: 'assistant',
        content: 'Search failed. Please try again.'
      }
      setMessages(prev => [...prev, errorMessage])
    }
    
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-chat">
        <h1 className="text-3xl font-bold mb-8 text-center">TuneScout Chat</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <ChatHistory messages={messages} resultCache={resultCache} />
          <ChatInput onSend={handleSendMessage} loading={loading} />
        </div>
      </div>
    </div>
  )
}
