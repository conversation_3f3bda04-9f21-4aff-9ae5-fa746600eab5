"""
Test Suite for AI Discogs Application

This module provides comprehensive tests for the AI Discogs application.
"""

import unittest
import os
import json
import tempfile
import shutil
from unittest.mock import patch, MagicMock

from src.services.discogs_service import DiscogsService
from src.services.nlp_service import NLPService
from src.services.auth_service import AuthenticationService, UserProfileService
from src.services.database_service import DatabaseService
from src.services.settings_service import SettingsService
from src.models.database_models import Base, User, UserProfile, UserSettings, ChatSession, ChatMessage

class TestDiscogsService(unittest.TestCase):
    """Test cases for the Discogs service."""
    
    def setUp(self):
        """Set up test environment."""
        self.service = DiscogsService(user_token="test_token")
    
    @patch('discogs_client.Client')
    def test_initialization(self, mock_client):
        """Test service initialization."""
        service = DiscogsService(user_token="test_token")
        mock_client.assert_called_once_with("AI-DiscogsApp/1.0", user_token="test_token")
    
    @patch('discogs_client.Client')
    def test_search(self, mock_client):
        """Test search functionality."""
        # Mock the search results
        mock_result = MagicMock()
        mock_result.id = 1
        mock_result.type = "artist"
        mock_result.title = "Test Artist"
        
        mock_results = MagicMock()
        mock_results.page = 1
        mock_results.pages = 1
        mock_results.per_page = 10
        mock_results.count = 1
        mock_results.__iter__.return_value = [mock_result]
        
        mock_client_instance = mock_client.return_value
        mock_client_instance.search.return_value = mock_results
        
        service = DiscogsService(user_token="test_token")
        results = service.search("test query", search_type="artist")
        
        mock_client_instance.search.assert_called_once_with("test query", page=1, per_page=10, type="artist")
        self.assertEqual(results['pagination']['items'], 1)
        self.assertEqual(len(results['results']), 1)
        self.assertEqual(results['results'][0]['id'], 1)
        self.assertEqual(results['results'][0]['type'], "artist")
        self.assertEqual(results['results'][0]['title'], "Test Artist")


class TestNLPService(unittest.TestCase):
    """Test cases for the NLP service."""
    
    def setUp(self):
        """Set up test environment."""
        self.service = NLPService()
    
    def test_process_query_artist(self):
        """Test processing a query for an artist."""
        query = "find artist Radiohead"
        result = self.service.process_query(query)
        
        self.assertTrue(result['matched_pattern'])
        self.assertEqual(result['entity_type'], 'artist')
        self.assertEqual(result['query'], "find artist radiohead")
    
    def test_process_query_album(self):
        """Test processing a query for an album."""
        query = "show me albums by Radiohead"
        result = self.service.process_query(query)
        
        self.assertTrue(result['matched_pattern'])
        self.assertEqual(result['entity_type'], 'album')
        self.assertEqual(result['artist'], 'radiohead')
    
    def test_generate_search_params(self):
        """Test generating search parameters from processed query."""
        processed_query = {
            'matched_pattern': True,
            'entity_type': 'album',
            'artist': 'radiohead',
            'query': 'show me albums by radiohead'
        }
        
        params = self.service.generate_search_params(processed_query)
        
        self.assertEqual(params['type'], 'album')
        self.assertEqual(params['query'], 'radiohead')


class TestDatabaseService(unittest.TestCase):
    """Test cases for the database service."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary database
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, 'test.db')
        self.db_url = f"sqlite:///{self.db_path}"
        
        # Initialize the service with the test database
        self.service = DatabaseService(db_url=self.db_url)
        self.service.init_db()
    
    def tearDown(self):
        """Clean up after tests."""
        shutil.rmtree(self.temp_dir)
    
    def test_create_user(self):
        """Test creating a user."""
        user = self.service.create_user("testuser", "<EMAIL>")
        
        self.assertIsNotNone(user)
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.email, "<EMAIL>")
    
    def test_get_user_by_username(self):
        """Test getting a user by username."""
        self.service.create_user("testuser", "<EMAIL>")
        user = self.service.get_user_by_username("testuser")
        
        self.assertIsNotNone(user)
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.email, "<EMAIL>")
    
    def test_create_user_profile(self):
        """Test creating a user profile."""
        user = self.service.create_user("testuser", "<EMAIL>")
        profile = self.service.create_user_profile(user.id, {
            'display_name': 'Test User',
            'bio': 'Test bio',
            'location': 'Test location'
        })
        
        self.assertIsNotNone(profile)
        self.assertEqual(profile.display_name, 'Test User')
        self.assertEqual(profile.bio, 'Test bio')
        self.assertEqual(profile.location, 'Test location')
    
    def test_create_chat_session(self):
        """Test creating a chat session."""
        user = self.service.create_user("testuser", "<EMAIL>")
        session = self.service.create_chat_session(user.id, "Test Session")
        
        self.assertIsNotNone(session)
        self.assertEqual(session.user_id, user.id)
        self.assertEqual(session.title, "Test Session")
    
    def test_add_chat_message(self):
        """Test adding a chat message."""
        user = self.service.create_user("testuser", "<EMAIL>")
        session = self.service.create_chat_session(user.id, "Test Session")
        message = self.service.add_chat_message(session.id, "user", "Test message")
        
        self.assertIsNotNone(message)
        self.assertEqual(message.session_id, session.id)
        self.assertEqual(message.role, "user")
        self.assertEqual(message.content, "Test message")


class TestSettingsService(unittest.TestCase):
    """Test cases for the settings service."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for settings
        self.temp_dir = tempfile.mkdtemp()
        self.settings_path = os.path.join(self.temp_dir, 'app_settings.json')
        
        # Mock the database service
        self.mock_db_service = MagicMock()
        
        # Initialize the service
        with patch('os.path.join', return_value=self.settings_path):
            self.service = SettingsService(db_service=self.mock_db_service)
    
    def tearDown(self):
        """Clean up after tests."""
        shutil.rmtree(self.temp_dir)
    
    def test_get_app_setting(self):
        """Test getting an application setting."""
        # Set up test data
        self.service.app_settings = {
            'test_category': {
                'test_key': 'test_value'
            }
        }
        
        value = self.service.get_app_setting('test_category', 'test_key')
        self.assertEqual(value, 'test_value')
    
    def test_set_app_setting(self):
        """Test setting an application setting."""
        # Mock the save method
        with patch.object(self.service, 'save_app_settings', return_value=True):
            result = self.service.set_app_setting('test_category', 'test_key', 'test_value')
            
            self.assertTrue(result)
            self.assertEqual(self.service.app_settings['test_category']['test_key'], 'test_value')
    
    def test_get_user_settings(self):
        """Test getting user settings."""
        # Mock the database response
        mock_settings = MagicMock()
        mock_settings.theme = 'dark'
        mock_settings.items_per_page = 20
        mock_settings.email_notifications = True
        mock_settings.share_collection = True
        mock_settings.share_wantlist = False
        mock_settings.discogs_api_key = 'test_key'
        mock_settings.discogs_api_secret = 'test_secret'
        
        self.mock_db_service.get_user_settings.return_value = mock_settings
        
        settings = self.service.get_user_settings(1)
        
        self.mock_db_service.get_user_settings.assert_called_once_with(1)
        self.assertEqual(settings['theme'], 'dark')
        self.assertEqual(settings['items_per_page'], 20)
        self.assertTrue(settings['email_notifications'])
        self.assertTrue(settings['share_collection'])
        self.assertFalse(settings['share_wantlist'])
        self.assertEqual(settings['discogs_api_key'], 'test_key')
        self.assertEqual(settings['discogs_api_secret'], 'test_secret')


class TestAuthenticationService(unittest.TestCase):
    """Test cases for the authentication service."""
    
    def setUp(self):
        """Set up test environment."""
        self.service = AuthenticationService(consumer_key="test_key", consumer_secret="test_secret")
    
    @patch('requests.get')
    def test_get_request_token(self, mock_get):
        """Test getting a request token."""
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "oauth_token=test_token&oauth_token_secret=test_secret"
        mock_get.return_value = mock_response
        
        result = self.service.get_request_token("http://example.com/callback")
        
        self.assertEqual(result['oauth_token'], 'test_token')
        self.assertEqual(result['oauth_token_secret'], 'test_secret')
    
    def test_get_authorization_url(self):
        """Test getting an authorization URL."""
        url = self.service.get_authorization_url("test_token")
        self.assertEqual(url, "https://www.discogs.com/oauth/authorize?oauth_token=test_token")
    
    @patch('requests.post')
    def test_get_access_token(self, mock_post):
        """Test getting an access token."""
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "oauth_token=test_access_token&oauth_token_secret=test_access_secret"
        mock_post.return_value = mock_response
        
        result = self.service.get_access_token("test_token", "test_secret", "test_verifier")
        
        self.assertEqual(result['oauth_token'], 'test_access_token')
        self.assertEqual(result['oauth_token_secret'], 'test_access_secret')


class TestUserProfileService(unittest.TestCase):
    """Test cases for the user profile service."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a temporary file for profiles
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, 'profiles.json')
        
        # Initialize the service with the test file
        self.service = UserProfileService(db_path=self.db_path)
    
    def tearDown(self):
        """Clean up after tests."""
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_get_profile(self):
        """Test saving and getting a profile."""
        # Save a profile
        profile_data = {
            'username': 'testuser',
            'name': 'Test User',
            'bio': 'Test bio'
        }
        
        result = self.service.save_profile('testuser', profile_data)
        self.assertTrue(result)
        
        # Get the profile
        profile = self.service.get_profile('testuser')
        self.assertEqual(profile['username'], 'testuser')
        self.assertEqual(profile['name'], 'Test User')
        self.assertEqual(profile['bio'], 'Test bio')
    
    def test_update_profile(self):
        """Test updating a profile."""
        # Save a profile
        profile_data = {
            'username': 'testuser',
            'name': 'Test User',
            'bio': 'Test bio'
        }
        
        self.service.save_profile('testuser', profile_data)
        
        # Update the profile
        updates = {
            'name': 'Updated Name',
            'location': 'Test Location'
        }
        
        result = self.service.update_profile('testuser', updates)
        self.assertTrue(result)
        
        # Get the updated profile
        profile = self.service.get_profile('testuser')
        self.assertEqual(profile['name'], 'Updated Name')
        self.assertEqual(profile['bio'], 'Test bio')
        self.assertEqual(profile['location'], 'Test Location')
    
    def test_delete_profile(self):
        """Test deleting a profile."""
        # Save a profile
        profile_data = {
            'username': 'testuser',
            'name': 'Test User',
            'bio': 'Test bio'
        }
        
        self.service.save_profile('testuser', profile_data)
        
        # Delete the profile
        result = self.service.delete_profile('testuser')
        self.assertTrue(result)
        
        # Try to get the deleted profile
        profile = self.service.get_profile('testuser')
        self.assertEqual(profile, {'username': 'testuser'})


if __name__ == '__main__':
    unittest.main()
