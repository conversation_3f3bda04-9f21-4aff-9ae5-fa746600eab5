from flask import Blueprint, jsonify, request
from src.services.discogs_service import DiscogsService
from src.services.analytics_service import AnalyticsService
import logging

logger = logging.getLogger(__name__)
search_bp = Blueprint('search', __name__)
discogs = DiscogsService()
analytics = AnalyticsService()

@search_bp.route('/search', methods=['GET'])
def search():
    """Search for music with tracking."""
    query = request.args.get('q', '')
    search_type = request.args.get('type', 'release')
    genre = request.args.get('genre', '')
    year = request.args.get('year', '')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 20)), 50)
    
    if not query:
        return jsonify({'error': 'Query required'}), 400
    
    # Track search query
    analytics.track_search(query, search_type, genre, year)
    
    # Perform search
    results = discogs.search(
        query=query,
        search_type=search_type,
        genre=genre if genre else None,
        year=year if year else None,
        page=page,
        per_page=per_page
    )
    
    # Add tracking IDs to results
    for i, result in enumerate(results.get('results', [])):
        result['tracking_id'] = f"{query}_{search_type}_{page}_{i}"
    
    return jsonify(results)

@search_bp.route('/track-view', methods=['POST'])
def track_view():
    """Track result card view."""
    data = request.json
    tracking_id = data.get('tracking_id')
    result_id = data.get('result_id')
    result_type = data.get('result_type')
    
    analytics.track_view(tracking_id, result_id, result_type)
    return jsonify({'status': 'tracked'})

@search_bp.route('/track-click', methods=['POST'])
def track_click():
    """Track result card click."""
    data = request.json
    tracking_id = data.get('tracking_id')
    result_id = data.get('result_id')
    result_type = data.get('result_type')
    action = data.get('action', 'details')
    
    analytics.track_click(tracking_id, result_id, result_type, action)
    return jsonify({'status': 'tracked'})

@search_bp.route('/filters', methods=['GET'])
def get_filters():
    """Get available filters."""
    return jsonify({
        'genres': analytics.get_popular_genres(),
        'years': list(range(1950, 2025)),
        'types': ['release', 'artist', 'label']
    })
