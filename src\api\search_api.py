"""
Search API endpoints with Swagger documentation
"""

from flask import request
from flask_restx import Resource, Namespace
from src.services.discogs_service import DiscogsService
from src.services.analytics_service import AnalyticsService
from src.api import (
    search_response_model, 
    track_view_model, 
    track_click_model, 
    filters_model,
    error_model
)
import logging

logger = logging.getLogger(__name__)

# Create namespace for search operations
search_ns = Namespace('search', description='Music search operations')

# Initialize services
discogs = DiscogsService()
analytics = AnalyticsService()

@search_ns.route('/search')
class SearchResource(Resource):
    @search_ns.doc('search_music')
    @search_ns.param('q', 'Search query', required=True, type='string')
    @search_ns.param('type', 'Search type', type='string', default='release', 
                     enum=['release', 'artist', 'label'])
    @search_ns.param('genre', 'Filter by genre', type='string')
    @search_ns.param('year', 'Filter by year', type='string')
    @search_ns.param('page', 'Page number', type='integer', default=1)
    @search_ns.param('per_page', 'Results per page (max 50)', type='integer', default=20)
    @search_ns.marshal_with(search_response_model)
    @search_ns.response(400, 'Bad Request', error_model)
    @search_ns.response(500, 'Internal Server Error', error_model)
    def get(self):
        """Search for music with tracking"""
        query = request.args.get('q', '')
        search_type = request.args.get('type', 'release')
        genre = request.args.get('genre', '')
        year = request.args.get('year', '')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)
        
        if not query:
            search_ns.abort(400, 'Query parameter is required')
        
        try:
            # Track search query
            analytics.track_search(query, search_type, genre, year)
            
            # Perform search
            results = discogs.search(
                query=query,
                search_type=search_type,
                genre=genre if genre else None,
                year=year if year else None,
                page=page,
                per_page=per_page
            )
            
            # Add tracking IDs to results
            for i, result in enumerate(results.get('results', [])):
                result['tracking_id'] = f"{query}_{search_type}_{page}_{i}"
            
            return results
            
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            search_ns.abort(500, f'Search failed: {str(e)}')


@search_ns.route('/track-view')
class TrackViewResource(Resource):
    @search_ns.doc('track_view')
    @search_ns.expect(track_view_model)
    @search_ns.response(200, 'Success')
    @search_ns.response(400, 'Bad Request', error_model)
    def post(self):
        """Track result card view for analytics"""
        data = request.json
        
        if not data:
            search_ns.abort(400, 'Request body is required')
            
        tracking_id = data.get('tracking_id')
        result_id = data.get('result_id')
        result_type = data.get('result_type')
        
        if not all([tracking_id, result_id, result_type]):
            search_ns.abort(400, 'tracking_id, result_id, and result_type are required')
        
        try:
            analytics.track_view(tracking_id, result_id, result_type)
            return {'status': 'tracked'}
        except Exception as e:
            logger.error(f"Track view error: {str(e)}")
            search_ns.abort(500, f'Tracking failed: {str(e)}')


@search_ns.route('/track-click')
class TrackClickResource(Resource):
    @search_ns.doc('track_click')
    @search_ns.expect(track_click_model)
    @search_ns.response(200, 'Success')
    @search_ns.response(400, 'Bad Request', error_model)
    def post(self):
        """Track result card click for analytics"""
        data = request.json
        
        if not data:
            search_ns.abort(400, 'Request body is required')
            
        tracking_id = data.get('tracking_id')
        result_id = data.get('result_id')
        result_type = data.get('result_type')
        action = data.get('action', 'details')
        
        if not all([tracking_id, result_id, result_type]):
            search_ns.abort(400, 'tracking_id, result_id, and result_type are required')
        
        try:
            analytics.track_click(tracking_id, result_id, result_type, action)
            return {'status': 'tracked'}
        except Exception as e:
            logger.error(f"Track click error: {str(e)}")
            search_ns.abort(500, f'Tracking failed: {str(e)}')


@search_ns.route('/filters')
class FiltersResource(Resource):
    @search_ns.doc('get_filters')
    @search_ns.marshal_with(filters_model)
    @search_ns.response(500, 'Internal Server Error', error_model)
    def get(self):
        """Get available search filters"""
        try:
            return {
                'genres': analytics.get_popular_genres(),
                'years': list(range(1950, 2025)),
                'types': ['release', 'artist', 'label']
            }
        except Exception as e:
            logger.error(f"Get filters error: {str(e)}")
            search_ns.abort(500, f'Failed to get filters: {str(e)}')
