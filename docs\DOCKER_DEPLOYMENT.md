# Docker Deployment Guide for TuneScout

This guide explains how to deploy the TuneScout app in a Docker Desktop cluster environment.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose installed
- Basic knowledge of Docker and container orchestration
- Discogs Personal Access Token (PAT)

## Files Overview

- `Dockerfile`: Defines the container image for the application
- `docker-compose.yml`: Orchestrates the application deployment
- `docker_setup.sh`: Script that adds health endpoint and configures the app for Docker

## Deployment Steps

### 1. Environment Variables

For Discogs API integration, set your Personal Access Token:

```bash
export DISCOGS_TOKEN=your_token_here
```

You can generate a token in your Discogs account settings under the "Developer" section.

### 2. Build and Start the Application

```bash
# Navigate to the application directory
cd tunescout

# Build and start the application
docker-compose up -d
```

### 3. Verify Deployment

```bash
# Check if the container is running
docker-compose ps

# Check the logs
docker-compose logs -f
```

The application should be accessible at http://localhost:1975

### 4. Deploying to Docker Desktop Kubernetes Cluster

If you want to deploy to Kubernetes in Docker Desktop:

1. Enable Kubernetes in Docker Desktop settings
2. Use kubectl to deploy:

```bash
# Generate a Kubernetes deployment file
kubectl kustomize . > k8s-deployment.yaml

# Apply the deployment
kubectl apply -f k8s-deployment.yaml
```

## Persistent Storage

The application uses a Docker volume (`discogs-data`) to persist data between container restarts. This ensures that:

- User profiles are preserved
- Settings are maintained
- Chat history is retained

## Scaling

To scale the application:

```bash
# Scale to multiple instances
docker-compose up -d --scale tunescout=3
```

Note: When scaling, ensure you have proper load balancing configured.

## Troubleshooting

- **Container fails to start**: Check logs with `docker-compose logs tunescout`
- **API rate limiting issues**: Verify your Discogs API token is set correctly
- **Data persistence issues**: Ensure the volume is properly mounted

## Health Checks

The application includes a `/health` endpoint that returns a 200 OK response when the service is healthy. Docker uses this to determine if the container is functioning properly.

## Environment Configuration

The following environment variables can be configured:

- `DISCOGS_TOKEN`: Your Discogs API Personal Access Token
- `FLASK_ENV`: Set to 'production' by default
- `FLASK_DEBUG`: Set to 0 (disabled) by default

You can modify these in the docker-compose.yml file or pass them as environment variables.
